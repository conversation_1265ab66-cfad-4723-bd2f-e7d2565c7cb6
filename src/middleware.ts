import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getSessionByToken } from './lib/auth-utils';
import { checkPremiumFeatureAccess } from './middleware/subscription-check';

export async function middleware(req: NextRequest) {
  // Log environment variables (sanitized) for debugging
  console.log('Middleware - Environment Variables:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set');
  console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set');
  console.log('- NEXT_PUBLIC_SITE_URL:', process.env.NEXT_PUBLIC_SITE_URL);

  // Skip middleware for debug page and API routes
  if (req.nextUrl.pathname.startsWith('/debug') || req.nextUrl.pathname.startsWith('/api')) {
    console.log('Middleware: Skipping debug page or API route', req.nextUrl.pathname);
    return NextResponse.next();
  }

  console.log('Middleware: Processing route', req.nextUrl.pathname);

  // Define protected and auth routes
  const isProtectedRoute = req.nextUrl.pathname.startsWith('/dashboard') ||
    req.nextUrl.pathname.startsWith('/assets') ||
    req.nextUrl.pathname.startsWith('/vault') ||
    req.nextUrl.pathname.startsWith('/trustees') ||
    req.nextUrl.pathname.startsWith('/settings') ||
    req.nextUrl.pathname.startsWith('/time-capsule') ||
    req.nextUrl.pathname.startsWith('/last-wishes') ||
    req.nextUrl.pathname.startsWith('/service-sunset') ||
    req.nextUrl.pathname.startsWith('/will') ||
    req.nextUrl.pathname.startsWith('/subscription');

  const isAuthRoute = req.nextUrl.pathname.startsWith('/login') ||
    req.nextUrl.pathname.startsWith('/register') ||
    req.nextUrl.pathname.startsWith('/verify');

  // Get the session token from cookies
  const sessionToken = req.cookies.get('session_token')?.value;
  let isAuthenticated = false;

  // If there's a session token, verify it
  if (sessionToken) {
    try {
      // Get the session
      const session = await getSessionByToken(sessionToken);
      isAuthenticated = !!session;
      console.log('Middleware: Authentication status', { isAuthenticated, userId: session?.user_id });
    } catch (error) {
      console.error('Error in middleware:', error);
      isAuthenticated = false;
    }
  }

  // Redirect unauthenticated users trying to access protected routes to login
  if (isProtectedRoute && !isAuthenticated) {
    console.log('Middleware: Redirecting unauthenticated user to login');
    const url = new URL('/login', req.url);
    url.searchParams.set('redirect', req.nextUrl.pathname);
    return NextResponse.redirect(url);
  }

  // Redirect authenticated users trying to access auth routes to dashboard
  if (isAuthRoute && isAuthenticated) {
    console.log('Middleware: Redirecting authenticated user to dashboard');
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }

  // Check premium feature access for authenticated users
  if (isAuthenticated) {
    // Check time capsule access
    if (req.nextUrl.pathname.startsWith('/time-capsule')) {
      const featureCheck = await checkPremiumFeatureAccess(req, 'timeCapsule');
      if (featureCheck) {
        return featureCheck; // Redirect to pricing page
      }
    }
  }

  return NextResponse.next();
}

// Specify which routes this middleware should run on
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/assets/:path*',
    '/vault/:path*',
    '/trustees/:path*',
    '/settings/:path*',
    '/time-capsule/:path*',
    '/last-wishes/:path*',
    '/service-sunset/:path*',
    '/will/:path*',
    '/subscription/:path*',
    '/login',
    '/register',
    '/verify',
    '/debug',
    // Include API routes but exclude them from authentication checks
    '/api/:path*',
  ],
};
