// Types for the Service Sunset feature

export type ServiceCategory =
  | 'subscription'
  | 'utility'
  | 'financial'
  | 'entertainment'
  | 'other';

export type ServicePriority = 'high' | 'medium' | 'low';

// Cancellation method type removed as it's not needed

export interface ServiceSunset {
  id: string;
  user_id: string;
  name: string;
  category: ServiceCategory;
  custom_category?: string;
  description?: string;
  website?: string;
  account_number?: string;
  contact_info?: string;
  cancellation_instructions?: string;
  priority: ServicePriority;
  auto_renewal: boolean;
  renewal_date?: string;
  cost_per_period?: number;
  period?: string;
  created_at: string;
  updated_at: string;
}

export const SERVICE_CATEGORIES: { value: ServiceCategory; label: string }[] = [
  { value: 'subscription', label: 'Subscription Service' },
  { value: 'utility', label: 'Utility' },
  { value: 'financial', label: 'Financial Service' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'other', label: 'Other' },
];

// CANCELLATION_METHODS constant removed as it's not needed

export const SERVICE_PRIORITIES: { value: ServicePriority; label: string; description: string }[] = [
  {
    value: 'high',
    label: 'High',
    description: 'Services that should be canceled immediately (e.g., financial services with recurring charges)'
  },
  {
    value: 'medium',
    label: 'Medium',
    description: 'Services that should be canceled within a month (e.g., utilities, subscriptions)'
  },
  {
    value: 'low',
    label: 'Low',
    description: 'Services that can be canceled eventually (e.g., free accounts, email lists)'
  },
];
