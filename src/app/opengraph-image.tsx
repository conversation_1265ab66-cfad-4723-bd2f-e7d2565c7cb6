import { ImageResponse } from 'next/og';

// Route segment config
export const runtime = 'edge';

// Image metadata
export const alt = 'Legalock - Secure Your Digital Legacy';
export const size = { width: 1200, height: 630 };

// Image generation
export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(to right, #1e3a8a, #3730a3)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '40px',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '20px',
          }}
        >
          <div
            style={{
              fontSize: '64px',
              color: 'white',
              fontWeight: 'bold',
              marginRight: '20px',
            }}
          >
            🛡️
          </div>
          <div
            style={{
              fontSize: '64px',
              color: 'white',
              fontWeight: 'bold',
            }}
          >
            Legalock
          </div>
        </div>
        <div
          style={{
            fontSize: '36px',
            color: 'white',
            marginTop: '20px',
            textAlign: 'center',
          }}
        >
          Secure Your Digital Legacy
        </div>
      </div>
    ),
    {
      ...size,
    }
  );
}
