import React from 'react';
import { <PERSON>, <PERSON>, Server, Eye, <PERSON><PERSON>he<PERSON>, RefreshCw } from 'lucide-react';
import SimplePageLayout from '@/components/Layout/SimplePageLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata = {
  title: 'Security | Legalock',
  description: 'Learn about the security measures Legalock implements to protect your sensitive information.',
};

export default function SecurityPage() {
  return (
    <SimplePageLayout title="Security">
      <div className="space-y-12">
        <div>
          <p className="text-lg text-gray-600 mb-8">
            At Legalock, we understand that you're entrusting us with sensitive information about your digital legacy. We take this responsibility seriously and have implemented comprehensive security measures to protect your data.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <div className="flex items-center mb-2">
                <Lock className="h-6 w-6 text-primary mr-3" />
                <CardTitle>End-to-End Encryption</CardTitle>
              </div>
              <CardDescription>
                Military-grade protection for your documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                All documents stored in your Digital Vault are encrypted using AES-256 encryption, one of the strongest encryption standards available. This ensures that your sensitive information remains private and secure.
              </p>
              <p className="text-gray-600 mt-2">
                Your data is encrypted before it leaves your device and remains encrypted while stored on our servers. Only you and your designated trustees (after your passing is verified) can decrypt and access this information.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center mb-2">
                <Shield className="h-6 w-6 text-primary mr-3" />
                <CardTitle>Secure Authentication</CardTitle>
              </div>
              <CardDescription>
                Multi-layered access controls
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                We implement robust authentication mechanisms, including email verification, strong password requirements, and optional two-factor authentication to ensure only authorized users can access accounts.
              </p>
              <p className="text-gray-600 mt-2">
                For trustee access after a user's passing, we require a thorough verification process to confirm the user's death before granting access to any information.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center mb-2">
                <Server className="h-6 w-6 text-primary mr-3" />
                <CardTitle>Secure Infrastructure</CardTitle>
              </div>
              <CardDescription>
                Enterprise-grade hosting and protection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Legalock is built on secure, SOC 2 compliant cloud infrastructure with multiple layers of security, including firewalls, intrusion detection systems, and regular security audits.
              </p>
              <p className="text-gray-600 mt-2">
                Our databases are protected by multiple security layers and are regularly backed up to prevent data loss while maintaining strict privacy controls.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center mb-2">
                <Eye className="h-6 w-6 text-primary mr-3" />
                <CardTitle>Privacy by Design</CardTitle>
              </div>
              <CardDescription>
                Built with privacy as a foundation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Our platform is designed with privacy as a core principle. We implement strict access controls, data minimization practices, and follow the principle of least privilege for all system access.
              </p>
              <p className="text-gray-600 mt-2">
                We never sell your data or share it with third parties except as necessary to provide our services or as required by law.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center mb-2">
                <FileCheck className="h-6 w-6 text-primary mr-3" />
                <CardTitle>Compliance</CardTitle>
              </div>
              <CardDescription>
                Meeting industry standards and regulations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Legalock is designed to comply with relevant data protection regulations, including GDPR and CCPA. We regularly review our practices to ensure ongoing compliance with evolving privacy laws.
              </p>
              <p className="text-gray-600 mt-2">
                Our team undergoes regular security training to ensure we maintain the highest standards of data protection and privacy.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center mb-2">
                <RefreshCw className="h-6 w-6 text-primary mr-3" />
                <CardTitle>Continuous Monitoring</CardTitle>
              </div>
              <CardDescription>
                Proactive security oversight
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Our systems are continuously monitored for suspicious activities and potential security threats. We employ automated systems and security professionals to detect and respond to issues in real-time.
              </p>
              <p className="text-gray-600 mt-2">
                We regularly conduct security assessments and penetration testing to identify and address potential vulnerabilities before they can be exploited.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="bg-gray-50 p-8 rounded-lg border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Security Commitment</h2>
          <p className="text-gray-600 mb-4">
            Security is not a one-time effort but an ongoing commitment. We continuously review and enhance our security measures to protect your data against evolving threats. Our team stays informed about the latest security best practices and implements improvements regularly.
          </p>
          <p className="text-gray-600">
            If you have questions about our security practices or want to report a security concern, please contact our security team at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>.
          </p>
        </div>
      </div>
    </SimplePageLayout>
  );
}
