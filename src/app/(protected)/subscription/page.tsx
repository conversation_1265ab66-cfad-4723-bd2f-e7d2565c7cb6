"use client";

import React from 'react';
import SubscriptionPlans from '@/components/Subscription/SubscriptionPlans';
import { useSubscription } from '@/context/SubscriptionContext';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function SubscriptionPage() {
  const { plan, isSubscribed, isLoading, subscriptionDetails } = useSubscription();

  return (
    <div className="container max-w-7xl mx-auto px-4 py-8">
      <div className="mb-10">
        <h1 className="text-3xl font-bold mb-2">Subscription Plans</h1>
        <p className="text-gray-600">
          Choose the plan that best fits your needs. Upgrade anytime to access more features.
        </p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="ml-4 text-lg text-gray-600">Loading subscription details...</p>
        </div>
      ) : (
        <>
          {isSubscribed && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-green-800 mb-2">
                Active Subscription: {plan.charAt(0).toUpperCase() + plan.slice(1)} Plan
              </h2>
              {subscriptionDetails && (
                <p className="text-green-700">
                  Your subscription renews on {new Date(subscriptionDetails.currentPeriodEnd).toLocaleDateString()}
                </p>
              )}
              <p className="mt-2 text-gray-600">
                You have access to all {plan} features. To manage your subscription or billing, visit your account settings.
              </p>
            </div>
          )}

          <SubscriptionPlans />

          <div className="mt-12 text-center text-sm text-gray-500">
            <p>All plans include a 14-day money-back guarantee if you're not satisfied.</p>
            <p className="mt-1">Have questions about our plans? <Link href="/contact" className="text-primary hover:underline">Contact our support team</Link>.</p>
          </div>
        </>
      )}
    </div>
  );
}