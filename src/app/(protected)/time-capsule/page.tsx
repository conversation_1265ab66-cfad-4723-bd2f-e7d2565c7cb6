"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase-unified';
import type { Tables } from '@/types/database.types';
import {
  Card,
  CardContent,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle, Clock, CalendarClock, ArrowLeft, Pencil, Trash2, Mail } from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import PageHeading from '@/components/ui/PageHeading';
import TimeCapsuleForm from '@/components/TimeCapsule/TimeCapsuleForm';
import TimeCapsuleView from '@/components/TimeCapsule/TimeCapsuleView';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function TimeCapsulePage() {
  const { user } = useAuth();
  const [timeCapsules, setTimeCapsules] = useState<Tables<'time_capsules'>[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCapsule, setSelectedCapsule] = useState<Tables<'time_capsules'> | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);

  useEffect(() => {
    if (user) {
      fetchTimeCapsules();
    }
  }, [user]);

  const fetchTimeCapsules = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/time-capsules');

      if (!response.ok) {
        throw new Error('Failed to fetch time capsules');
      }

      const data = await response.json();
      setTimeCapsules(data || []);
    } catch (error) {
      console.error('Error fetching time capsules:', error);
      toast.error('Failed to load time capsules');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTimeCapsule = () => {
    setSelectedCapsule(null);
    setIsFormOpen(true);
  };

  const handleEditTimeCapsule = (capsule: Tables<'time_capsules'>) => {
    setSelectedCapsule(capsule);
    setIsFormOpen(true);
  };

  const handleViewTimeCapsule = (capsule: Tables<'time_capsules'>) => {
    setSelectedCapsule(capsule);
    setIsViewOpen(true);
  };

  const handleDeleteTimeCapsule = (capsule: Tables<'time_capsules'>) => {
    setSelectedCapsule(capsule);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTimeCapsule = async () => {
    if (!selectedCapsule) return;

    try {
      const response = await fetch(`/api/time-capsules?id=${selectedCapsule.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete time capsule');
      }

      toast.success('Time capsule deleted successfully');
      fetchTimeCapsules();
    } catch (error: any) {
      console.error('Error deleting time capsule:', error);
      toast.error(error.message || 'Error deleting time capsule');
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedCapsule(null);
    }
  };

  const handleAddTimeCapsule = async (data: any) => {
    try {
      const response = await fetch('/api/time-capsules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add time capsule');
      }

      toast.success('Time capsule created successfully');
      setIsFormOpen(false);
      fetchTimeCapsules();
    } catch (error: any) {
      console.error('Error adding time capsule:', error);
      toast.error(error.message || 'Error adding time capsule');
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Time Capsules"
        description="Create messages to be delivered to your loved ones at a specific date and time in the future."
        actions={
          timeCapsules.length > 0 && (
            <Button onClick={handleCreateTimeCapsule}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Time Capsule
            </Button>
          )
        }
      />

      <div className="mt-6">
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">
              Your Time Capsules
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({timeCapsules.length} {timeCapsules.length === 1 ? 'capsule' : 'capsules'})
              </span>
            </h3>
            {isLoading ? (
              // Loading skeletons
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                    <Skeleton className="h-8 w-16" />
                  </div>
                ))}
              </div>
            ) : timeCapsules.length === 0 ? (
              // Empty state
              <div className="text-center py-12">
                <Clock className="mx-auto h-12 w-12 text-gray-400" />
                <h4 className="mt-4 text-lg font-medium text-gray-900">No Time Capsules Yet</h4>
                <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                  Create a time capsule with messages and attachments to be delivered to your loved ones at a specific date and time in the future.
                </p>
                <Button onClick={handleCreateTimeCapsule} className="mt-4">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Create Your First Time Capsule
                </Button>
              </div>
            ) : (
              // Time capsule list
              <div className="space-y-4">
                {timeCapsules.map((capsule) => (
                  <div key={capsule.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 rounded-full p-2">
                        <Clock className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{capsule.title}</h4>
                        <p className="text-sm text-gray-500">
                          To: {capsule.recipient_name || `${capsule.recipient_first_name || ''} ${capsule.recipient_last_name || ''}`} ({capsule.recipient_email})
                        </p>
                        <div className="flex items-center mt-1 text-xs text-gray-500">
                          <CalendarClock className="mr-1 h-3 w-3" />
                          <span>
                            {format(new Date(capsule.delivery_date), 'MMM d, yyyy')} at {capsule.delivery_hour?.toString().padStart(2, '0') || '12'}:00
                          </span>
                          <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-medium
                            ${capsule.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                              capsule.status === 'delivered' ? 'bg-green-100 text-green-800' :
                                'bg-gray-100 text-gray-800'}`}>
                            {capsule.status.charAt(0).toUpperCase() + capsule.status.slice(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleViewTimeCapsule(capsule)}>
                        View Details
                      </Button>

                      {capsule.status === 'scheduled' && (
                        <Button variant="ghost" size="sm" onClick={() => handleDeleteTimeCapsule(capsule)}>
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Time Capsule Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedCapsule ? 'Edit Time Capsule' : 'Create New Time Capsule'}</DialogTitle>
            <DialogDescription>
              {selectedCapsule
                ? 'Update your scheduled time capsule details'
                : 'Create a message that will be delivered at a specific date and time in the future'}
            </DialogDescription>
          </DialogHeader>
          <TimeCapsuleForm
            onSubmit={handleAddTimeCapsule}
            onCancel={() => setIsFormOpen(false)}
            defaultValues={selectedCapsule || undefined}
          />
        </DialogContent>
      </Dialog>

      {/* Time Capsule View Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="sm:max-w-[700px]">
          {selectedCapsule && (
            <TimeCapsuleView
              capsule={selectedCapsule}
              onBack={() => setIsViewOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the time capsule "{selectedCapsule?.title}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteTimeCapsule} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}