@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 173 80% 36%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 221.2 83.2% 53.3%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 223 47% 14%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 223 47% 14%;
    --sidebar-ring: 221.2 83.2% 53.3%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 182.9 73.1% 32.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 221.2 83.2% 53.3%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .legalock-card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .dashboard-card {
    @apply legalock-card p-6;
  }

  .feature-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow h-full;
  }

  .feature-card-icon {
    @apply w-10 h-10 rounded-full flex items-center justify-center mr-3;
  }

  .feature-card-title {
    @apply text-lg font-semibold;
  }

  .feature-card-subtitle {
    @apply text-sm text-gray-500;
  }

  .feature-card-action {
    @apply text-sm font-medium flex items-center hover:underline transition-colors;
  }

  .feature-card-empty {
    @apply rounded-lg p-6 text-center;
  }

  .feature-card-empty-icon {
    @apply h-10 w-10 mx-auto mb-3 opacity-70;
  }

  .feature-card-empty-title {
    @apply text-base font-medium mb-2;
  }

  .feature-card-empty-description {
    @apply text-sm mb-4;
  }

  .feature-card-empty-button {
    @apply inline-flex items-center justify-center px-4 py-2 text-white rounded-md transition-colors font-medium text-sm;
  }

  .page-container {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
  }
}