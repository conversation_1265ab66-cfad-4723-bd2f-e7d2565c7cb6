import React from 'react';
import <PERSON> from 'next/link';
import { ArrowLeft, HelpCircle, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import SimplePageLayout from '@/components/Layout/SimplePageLayout';

// Sample category data with articles
const categories = {
  'getting-started': {
    title: 'Getting Started',
    description: 'Learn the basics of setting up your Legalock account and navigating the platform.',
    articles: [
      {
        id: 1,
        title: 'Creating Your First Legalock Account',
        description: 'Step-by-step guide to setting up your account and choosing the right plan.',
        slug: 'create-account',
        readTime: '3 min read'
      },
      {
        id: 2,
        title: 'Understanding Your Dashboard',
        description: 'Navigate your dashboard and understand each feature section.',
        slug: 'dashboard-overview',
        readTime: '5 min read'
      },
      {
        id: 3,
        title: 'Setting Up Your First Digital Asset',
        description: 'Learn how to document your first digital asset with access instructions.',
        slug: 'first-digital-asset',
        readTime: '4 min read'
      },
      {
        id: 4,
        title: 'Choosing Between Free and Premium Plans',
        description: 'Understand the differences and decide which plan is right for you.',
        slug: 'plan-differences',
        readTime: '3 min read'
      }
    ]
  },
  'account-security': {
    title: 'Account & Security',
    description: 'Understand how to manage your account settings and keep your information secure.',
    articles: [
      {
        id: 1,
        title: 'Setting Up Two-Factor Authentication',
        description: 'Add an extra layer of security to your account with 2FA.',
        slug: 'setup-2fa',
        readTime: '4 min read'
      },
      {
        id: 2,
        title: 'Changing Your Password',
        description: 'How to update your password and security best practices.',
        slug: 'change-password',
        readTime: '2 min read'
      },
      {
        id: 3,
        title: 'Account Recovery Options',
        description: 'Set up recovery methods in case you lose access to your account.',
        slug: 'account-recovery',
        readTime: '3 min read'
      },
      {
        id: 4,
        title: 'Understanding Data Privacy',
        description: 'Learn how we protect your data and your privacy rights.',
        slug: 'data-privacy',
        readTime: '6 min read'
      }
    ]
  },
  'digital-assets': {
    title: 'Digital Assets',
    description: 'Learn how to document and manage your digital assets effectively.',
    articles: [
      {
        id: 1,
        title: 'What Are Digital Assets?',
        description: 'Understanding different types of digital assets and why they matter.',
        slug: 'what-are-digital-assets',
        readTime: '4 min read'
      },
      {
        id: 2,
        title: 'Adding Social Media Accounts',
        description: 'Document your social media accounts with proper access instructions.',
        slug: 'add-social-media',
        readTime: '5 min read'
      },
      {
        id: 3,
        title: 'Managing Financial Accounts',
        description: 'Safely document online banking and investment accounts.',
        slug: 'financial-accounts',
        readTime: '6 min read'
      },
      {
        id: 4,
        title: 'Organizing Assets by Category',
        description: 'Use categories to organize your digital assets effectively.',
        slug: 'organize-assets',
        readTime: '3 min read'
      }
    ]
  },
  'trustees-contacts': {
    title: 'Trustees & Contacts',
    description: 'Understand how to designate trustees and manage emergency contacts.',
    articles: [
      {
        id: 1,
        title: 'How do I add a trustee to my account?',
        description: 'Complete guide to adding and managing trustees.',
        slug: 'add-trustee',
        readTime: '4 min read'
      },
      {
        id: 2,
        title: 'Trustee Responsibilities and Access',
        description: 'What trustees can and cannot do with your account.',
        slug: 'trustee-responsibilities',
        readTime: '5 min read'
      },
      {
        id: 3,
        title: 'Managing Emergency Contacts',
        description: 'Add and organize contacts for your trustees to notify.',
        slug: 'emergency-contacts',
        readTime: '3 min read'
      },
      {
        id: 4,
        title: 'How do trustees verify my passing?',
        description: 'The verification process trustees must complete.',
        slug: 'trustee-verification',
        readTime: '4 min read'
      }
    ]
  }
};

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function CategoryPage({ params }: PageProps) {
  const { slug } = await params;
  const category = categories[slug as keyof typeof categories];

  if (!category) {
    return (
      <SimplePageLayout title="Category Not Found">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Category Not Found</h2>
          <p className="text-gray-600 mb-6">The help category you're looking for doesn't exist.</p>
          <Button asChild>
            <Link href="/help">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Help Center
            </Link>
          </Button>
        </div>
      </SimplePageLayout>
    );
  }

  return (
    <SimplePageLayout title={category.title}>
      <div className="mb-6">
        <Button variant="outline" asChild>
          <Link href="/help">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Help Center
          </Link>
        </Button>
      </div>

      <div className="mb-8">
        <p className="text-lg text-gray-600">{category.description}</p>
      </div>

      <div className="grid gap-6">
        {category.articles.map((article) => (
          <Card key={article.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-xl mb-2">{article.title}</CardTitle>
                  <CardDescription className="text-base">{article.description}</CardDescription>
                </div>
                <div className="ml-4 text-sm text-gray-500">
                  {article.readTime}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Button variant="ghost" className="text-primary p-0 h-auto" asChild>
                <Link href={`/help/question/${article.slug}`} className="flex items-center">
                  Read article
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-12 bg-gray-100 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Need More Help?</h3>
        <p className="text-gray-600 mb-4">
          Can't find what you're looking for in this category? Try searching our help center or contact our support team.
        </p>
        <div className="flex gap-4">
          <Button variant="outline" asChild>
            <Link href="/help">Search Help Center</Link>
          </Button>
          <Button asChild>
            <Link href="/contact">Contact Support</Link>
          </Button>
        </div>
      </div>
    </SimplePageLayout>
  );
}
