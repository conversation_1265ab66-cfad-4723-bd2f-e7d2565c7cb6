"use client";

import Link from 'next/link';
import StructuredData from '@/components/StructuredData';
import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { Shield, Check, Database, Users, Clock, MessageSquare, HeadphonesIcon, UserPlus, HardDrive, UserCog, FileText, Sunset, Heart } from 'lucide-react';
import ScrollHeader from '@/components/ScrollHeader';

interface ListItemProps {
  title: string;
  href: string;
  children: React.ReactNode;
  icon: string;
}

const ListItem = ({ title, href, children, icon }: ListItemProps) => {
  let IconComponent;

  switch (icon) {
    case 'Database':
      IconComponent = Database;
      break;
    case 'HardDrive':
      IconComponent = HardDrive;
      break;
    case 'Users':
      IconComponent = Users;
      break;
    case 'UserPlus':
      IconComponent = UserPlus;
      break;
    case 'Sunset':
      IconComponent = Sunset;
      break;
    case 'Heart':
      IconComponent = Heart;
      break;
    default:
      IconComponent = Shield;
  }

  return (
    <li className="row-span-1">
      <Link
        href={href}
        className="flex flex-col h-full space-y-2 p-4 rounded-md hover:bg-gray-100 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <IconComponent className="h-5 w-5 text-primary" />
          <h3 className="text-base font-medium">{title}</h3>
        </div>
        <p className="text-sm text-gray-500">{children}</p>
      </Link>
    </li>
  );
};

export default function Home() {
  // Organization structured data
  const organizationData = {
    name: 'Legalock',
    url: 'https://legalock.com',
    logo: 'https://legalock.com/images/Legalock-logo.svg',
    sameAs: [
      'https://twitter.com/legalock',
      'https://facebook.com/legalock',
      'https://linkedin.com/company/legalock'
    ],
    description: 'Legalock helps you organize, protect, and pass on your digital assets and final wishes to your loved ones.',
  };

  // Website structured data
  const websiteData = {
    name: 'Legalock - Secure Your Digital Legacy',
    url: 'https://legalock.com',
    potentialAction: {
      '@type': 'SearchAction',
      'target': 'https://legalock.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  };

  return (
    <div className="flex flex-col min-h-screen pt-16">
      {/* Structured Data */}
      <StructuredData type="Organization" data={organizationData} />
      <StructuredData type="WebSite" data={websiteData} />
      {/* Header */}
      <ScrollHeader>
        <NavigationMenu className="hidden md:flex">
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger>Features</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[400px] gap-3 p-4 md:w-[600px] md:grid-cols-2 lg:w-[800px] lg:grid-cols-3">
                  <ListItem href="/features/digital-assets" title="Asset Management" icon="Database">
                    Create a comprehensive inventory of your digital and physical assets
                  </ListItem>
                  <ListItem href="/features/digital-vault" title="Digital Vault" icon="HardDrive">
                    Securely store sensitive documents for your trustees
                  </ListItem>
                  <ListItem href="/features/trustee-management" title="Trustee Management" icon="Users">
                    Designate trusted individuals to manage your digital legacy
                  </ListItem>
                  <ListItem href="/features/emergency-contacts" title="Emergency Contacts" icon="UserPlus">
                    Create a list of people to be notified after your passing
                  </ListItem>
                  <ListItem href="/features/service-sunset" title="Service Sunset" icon="Sunset">
                    List services and subscriptions to be canceled
                  </ListItem>
                  <ListItem href="/features/last-wishes" title="Last Wishes" icon="Heart">
                    Document your final wishes, organ donation status, and more
                  </ListItem>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/pricing" className={navigationMenuTriggerStyle()}>
                Pricing
              </Link>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </ScrollHeader>

      {/* Hero Section */}
      <header className="relative overflow-hidden bg-gradient-to-r from-blue-900 via-indigo-800 to-purple-900 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{ backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")' }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 relative z-10">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="w-full">
              <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100 leading-tight pb-2">
                Secure Your Digital Legacy with Legalock
              </h1>
              <p className="mt-6 text-xl text-blue-100 leading-relaxed">
                Legalock helps you organize, protect, and pass on your digital assets and final wishes to your loved ones. Create a comprehensive digital legacy plan today.
              </p>
              <div className="mt-10 flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-white text-blue-900 hover:bg-blue-50 transition-all duration-300 transform hover:translate-y-[-2px]" asChild>
                  <Link href="/signup/select-tier">Get Started</Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white bg-blue-700/50 hover:bg-blue-700/70 transition-all duration-300" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
              </div>
            </div>

            <div className="hidden md:block relative">
              <div className="absolute -top-16 -right-16 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-2xl opacity-20"></div>
              <div className="absolute -bottom-16 -left-16 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-2xl opacity-20"></div>
              <div className="absolute top-32 -right-8 w-72 h-72 bg-indigo-400 rounded-full mix-blend-multiply filter blur-2xl opacity-20"></div>

              <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl">
                <div className="flex items-center justify-center h-80 bg-gradient-to-br from-white/10 to-white/5 rounded-xl border border-white/10">
                  <div className="text-center">
                    <div className="w-24 h-24 bg-white/20 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                      <div className="w-16 h-16 bg-white/30 rounded-xl flex items-center justify-center">
                        <div className="w-8 h-8 bg-blue-200 rounded-lg"></div>
                      </div>
                    </div>
                    <p className="text-blue-100 text-lg font-medium">Image Placeholder</p>
                    <p className="text-blue-200 text-sm mt-2">Your custom hero image will appear here</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Wave Divider */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-white">
          <svg className="absolute -top-32 w-full h-32" preserveAspectRatio="none" viewBox="0 0 1440 80">
            <path fill="white" fillOpacity="1" d="M0,0 C240,50 480,65 720,50 C960,35 1200,15 1440,35 L1440,80 L0,80 Z"></path>
          </svg>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Features Section */}
        <section className="py-24 bg-white" id="features">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-20">
              <span className="bg-blue-100 text-blue-800 text-base font-semibold px-6 py-2 rounded-full inline-block mb-6 shadow-sm">Features</span>
              <h2 className="text-3xl font-bold text-gray-900 sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-blue-800 to-indigo-700 pb-2">
                Comprehensive Legacy Planning
              </h2>
              <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to organize your digital life and ensure your legacy is preserved.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
              {/* Feature 1 - Digital Asset Inventory */}
              <div className="group relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
                    <Database className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">Digital Asset Inventory</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    Create a comprehensive catalog of your digital and physical assets with secure access instructions for your trustees.
                  </p>
                  <Link href="/features/digital-assets" className="text-blue-600 font-medium inline-flex items-center group-hover:text-blue-700">
                    Learn more
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Feature 2 - Secure Document Vault */}
              <div className="group relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
                    <HardDrive className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">Secure Document Vault</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    Store important documents in an encrypted vault accessible only to designated trustees after your passing.
                  </p>
                  <Link href="/features/digital-vault" className="text-blue-600 font-medium inline-flex items-center group-hover:text-blue-700">
                    Learn more
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Feature 3 - Trustee Management */}
              <div className="group relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">Trustee Management</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    Designate trusted individuals to manage your digital legacy and control what information each trustee can access.
                  </p>
                  <Link href="/features/trustee-management" className="text-blue-600 font-medium inline-flex items-center group-hover:text-blue-700">
                    Learn more
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Feature 4 - Emergency Contacts */}
              <div className="group relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
                    <UserPlus className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">Emergency Contacts</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    Create a list of people who should be notified by your trustees after your passing with their contact information.
                  </p>
                  <Link href="/features/emergency-contacts" className="text-blue-600 font-medium inline-flex items-center group-hover:text-blue-700">
                    Learn more
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Feature 5 - Service Sunset */}
              <div className="group relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
                    <Sunset className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">Service Sunset</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    List services and subscriptions that should be canceled after your passing with detailed cancellation instructions.
                  </p>
                  <Link href="/features/service-sunset" className="text-blue-600 font-medium inline-flex items-center group-hover:text-blue-700">
                    Learn more
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Feature 6 - Last Wishes */}
              <div className="group relative bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
                    <Heart className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">Last Wishes</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    Document your final wishes, including funeral preferences, organ donation status, and pet care instructions.
                  </p>
                  <Link href="/features/last-wishes" className="text-blue-600 font-medium inline-flex items-center group-hover:text-blue-700">
                    Learn more
                    <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 bg-gray-50" id="pricing">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-20">
              <span className="bg-blue-50 text-blue-700 text-sm font-medium px-4 py-1.5 rounded-full inline-block mb-4">Pricing</span>
              <h2 className="text-3xl font-bold text-gray-900 sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-blue-800 to-indigo-700 pb-2">
                Secure Your Digital Legacy
              </h2>
              <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the plan that best protects what matters most.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {/* Free Plan - Essential Legacy */}
              <div className="group bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-xl flex flex-col">
                <div className="p-8">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900">Essential Legacy</h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Free
                    </span>
                  </div>
                  <div className="mt-5 flex items-baseline text-gray-900">
                    <span className="text-5xl font-extrabold tracking-tight">$0</span>
                    <span className="ml-1 text-xl font-medium text-gray-500">/forever</span>
                  </div>
                  <p className="mt-3 text-gray-500">For individuals just getting started with digital legacy planning.</p>
                </div>
                <div className="border-t border-gray-100 bg-gray-50 px-8 py-6 flex-grow">
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">Basic asset management</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">1 trustee</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">Will writing assistance</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">Service sunset planning</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">Email support</span>
                    </li>
                  </ul>
                </div>
                <div className="px-8 py-6 mt-auto">
                  <Button className="w-full bg-white hover:bg-gray-50 text-blue-700 border-blue-700 hover:border-blue-800 transition-colors" variant="outline" asChild>
                    <Link href="/register?tier=free">Get Started Free</Link>
                  </Button>
                </div>
              </div>

              {/* Premium Plan - Legacy Preserver */}
              <div className="group bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-xl flex flex-col">
                <div className="p-8">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900">Legacy Preserver</h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                      Premium
                    </span>
                  </div>
                  <div className="mt-5 flex items-baseline text-gray-900">
                    <span className="text-5xl font-extrabold tracking-tight">$29.99</span>
                    <span className="ml-1 text-xl font-medium text-gray-500">/year</span>
                  </div>
                  <p className="mt-3 text-gray-500">Complete protection for your digital legacy and peace of mind.</p>
                </div>
                <div className="border-t border-gray-100 bg-gray-50 px-8 py-6 flex-grow">
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-gray-700">Everything in Essential Legacy</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-gray-700">Unlimited digital assets</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-gray-700">Digital Vault with encryption</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-gray-700">Time Capsule messages</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-gray-700">Up to 5 trustees</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-3 mt-0.5">
                        <Check className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-gray-700">Priority support</span>
                    </li>
                  </ul>
                </div>
                <div className="px-8 py-6 mt-auto">
                  <Button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white transition-colors" asChild>
                    <Link href="/register?tier=premium">Get Started Premium</Link>
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-12 text-center">
              <p className="text-gray-600 text-sm">
                Start with Essential Legacy for free, no credit card required.
                <br />
                Need a custom solution for your organization? <Link href="/contact" className="text-primary font-medium">Contact us</Link> for enterprise pricing.
              </p>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <span className="bg-blue-100 text-blue-800 text-base font-semibold px-6 py-2 rounded-full inline-block mb-6 shadow-sm">Testimonials</span>
              <h2 className="text-3xl font-bold text-gray-900 sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-blue-800 to-indigo-700 pb-2">
                Trusted by Families Worldwide
              </h2>
              <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
                See how Legalock has helped families secure their digital legacy and gain peace of mind.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Testimonial 1 */}
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-lg">
                    SM
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-semibold text-gray-900">Sarah Mitchell</h4>
                    <p className="text-gray-600 text-sm">Legacy Preserver Member</p>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed mb-4">
                  "Legalock gave me complete peace of mind. I know my family will have access to everything they need, from my digital accounts to important documents. The trustee system is brilliant."
                </p>
                <div className="flex text-yellow-400">
                  <span>★★★★★</span>
                </div>
              </div>

              {/* Testimonial 2 */}
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-lg">
                    MJ
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-semibold text-gray-900">Michael Johnson</h4>
                    <p className="text-gray-600 text-sm">Essential Legacy Member</p>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed mb-4">
                  "As a tech professional, I had so many digital assets scattered everywhere. Legalock helped me organize everything in one secure place. My wife now knows exactly what to do."
                </p>
                <div className="flex text-yellow-400">
                  <span>★★★★★</span>
                </div>
              </div>

              {/* Testimonial 3 */}
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-lg">
                    ER
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-semibold text-gray-900">Emily Rodriguez</h4>
                    <p className="text-gray-600 text-sm">Legacy Preserver Member</p>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed mb-4">
                  "The time capsule feature is amazing! I've recorded messages for my children's future milestones. Legalock isn't just about death - it's about preserving memories and love."
                </p>
                <div className="flex text-yellow-400">
                  <span>★★★★★</span>
                </div>
              </div>
            </div>

            {/* CTA at bottom of testimonials */}
            <div className="mt-16 text-center">
              <div className="bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl shadow-xl p-12">
                <h3 className="text-3xl font-bold text-white mb-4">Ready to Secure Your Legacy?</h3>
                <p className="text-blue-100 text-lg mb-8 max-w-2xl mx-auto">
                  Join thousands of families who trust Legalock to protect their digital legacy and ensure their loved ones are taken care of.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-white text-blue-700 hover:bg-blue-50 transition-all duration-300 transform hover:translate-y-[-2px]" asChild>
                    <Link href="/register?tier=free">Get Started for Free</Link>
                  </Button>
                  <Button size="lg" variant="outline" className="border-white text-white bg-blue-700/50 hover:bg-blue-700/70 transition-all duration-300" asChild>
                    <Link href="/login">Sign In</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-100 text-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <Shield className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">Legalock</span>
              </div>
              <p className="text-gray-600 max-w-md">
                Secure your digital legacy and ensure your loved ones can access what they need when the time comes.
              </p>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">
                Resources
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/blog" className="text-gray-600 hover:text-blue-600 transition-colors">
                    Blog
                  </Link>
                </li>
                <li>
                  <Link href="/help" className="text-gray-600 hover:text-blue-600 transition-colors">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">
                    Contact Us
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">
                Legal
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/privacy" className="text-gray-600 hover:text-blue-600 transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="text-gray-600 hover:text-blue-600 transition-colors">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="/security" className="text-gray-600 hover:text-blue-600 transition-colors">
                    Security
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-300 text-center text-gray-600">
            <p>© {new Date().getFullYear()} Legalock. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
