"use client";

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase-unified';

export default function DebugEnvPage() {
  const [envStatus, setEnvStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function checkEnvironment() {
      try {
        setLoading(true);
        
        // Check client-side environment variables
        const clientEnv = {
          NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
          NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
          NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
        };
        
        // Check server-side environment variables via API
        const serverResponse = await fetch('/api/test-env');
        const serverEnv = await serverResponse.json();
        
        // Test Supabase connection
        let supabaseStatus = 'Unknown';
        try {
          const { error } = await supabase.from('custom_users').select('count(*)', { count: 'exact', head: true });
          supabaseStatus = error ? `Error: ${error.message}` : 'Connected';
        } catch (e: any) {
          supabaseStatus = `Exception: ${e.message}`;
        }
        
        setEnvStatus({
          client: clientEnv,
          server: serverEnv,
          supabase: supabaseStatus
        });
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    }
    
    checkEnvironment();
  }, []);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Environment Debug Page</h1>
      
      {loading ? (
        <p>Loading environment information...</p>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-red-600">{error}</p>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4">Client-Side Environment</h2>
            <pre className="bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(envStatus.client, null, 2)}
            </pre>
          </div>
          
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4">Server-Side Environment</h2>
            <pre className="bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(envStatus.server, null, 2)}
            </pre>
          </div>
          
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4">Supabase Connection</h2>
            <p className={envStatus.supabase.includes('Error') || envStatus.supabase.includes('Exception') ? 'text-red-600' : 'text-green-600'}>
              Status: {envStatus.supabase}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
