"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import VerificationSuccess from '@/components/Auth/VerificationSuccess';
import { Loader2 } from 'lucide-react';

export default function VerifySuccessPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // If no user is logged in, redirect to login
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    // If user is logged in, show the success page
    if (user) {
      setIsReady(true);
    }
  }, [user, loading, router]);

  if (loading || !isReady) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return <VerificationSuccess user={user} />;
}
