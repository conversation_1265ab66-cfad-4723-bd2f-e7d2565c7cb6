import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase-unified';
import { supabaseAdmin } from '@/lib/supabase-unified';

// Route segment config
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase client is initialized correctly
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    // Test Supabase connection
    let connectionTest = { success: false, error: null, data: null };

    try {
      // Use supabaseAdmin for the test since it has more permissions
      if (supabaseAdmin) {
        const { data, error } = await supabaseAdmin.from('custom_users').select('count(*)', { count: 'exact', head: true });
        connectionTest = {
          success: !error,
          error: error ? error.message : null,
          data: data || null,
        };
      } else {
        connectionTest = {
          success: false,
          error: 'supabaseAdmin is not initialized',
          data: null,
        };
      }
    } catch (connError: any) {
      connectionTest = {
        success: false,
        error: connError.message || 'Connection test failed',
        data: null,
      };
    }

    return NextResponse.json({
      success: true,
      message: 'Supabase client test',
      environment: {
        supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
        supabaseAnonKey: supabaseAnonKey ? 'Set' : 'Not set',
        supabaseServiceKey: supabaseServiceKey ? 'Set' : 'Not set',
        nodeEnv: process.env.NODE_ENV,
      },
      supabaseClient: {
        initialized: !!supabase,
        hasAuthMethods: supabase && typeof supabase.auth === 'object',
      },
      supabaseAdminClient: {
        initialized: !!supabaseAdmin,
      },
      connectionTest
    });
  } catch (error: any) {
    console.error('Error in test-supabase:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'An error occurred',
      stack: error.stack,
    }, { status: 500 });
  }
}
