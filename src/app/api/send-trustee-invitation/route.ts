import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { getSupabaseAdminClient } from '@/lib/supabase';
import { supabaseAdmin } from '@/lib/supabase-unified';
import { ensureAuthUserMapping } from '@/lib/auth-mapping-utils';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabase = getSupabaseAdminClient();

    const {
      inviterName,
      inviterEmail,
      trusteeName,
      trusteeEmail,
      permissions,
      message,
      inviteId,
    } = await request.json();

    // Validate required fields
    if (!trusteeEmail || !inviteId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // First, verify the trustee record exists
    const { data: trusteeData, error: fetchError } = await supabase
      .from('trustees')
      .select('*')
      .eq('id', inviteId)
      .single();

    if (fetchError || !trusteeData) {
      console.error('Error fetching trustee record:', fetchError);
      return NextResponse.json(
        { error: 'Trustee record not found' },
        { status: 404 }
      );
    }

    // Verify the email matches
    if (trusteeData.trustee_email !== trusteeEmail.toLowerCase().trim()) {
      console.error('Email mismatch:', {
        providedEmail: trusteeEmail,
        trusteeEmail: trusteeData.trustee_email
      });
      return NextResponse.json(
        { error: 'Email mismatch with trustee record' },
        { status: 400 }
      );
    }

    // Generate a secure token for additional verification
    const tokenBytes = new Uint8Array(32);
    crypto.getRandomValues(tokenBytes);
    const secureToken = Array.from(tokenBytes)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    // Store the token in the database for verification
    try {
      // First, clean up any existing tokens for this trustee
      await supabaseAdmin
        .from('trustee_tokens')
        .delete()
        .eq('trustee_id', inviteId);

      // Then insert the new token
      const { error: tokenError } = await supabaseAdmin
        .from('trustee_tokens')
        .insert({
          trustee_id: inviteId,
          token: secureToken,
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days expiry
        });

      if (tokenError) {
        console.error("Error storing trustee token:", tokenError);
        // Try one more time with the admin client
        const { error: retryError } = await supabaseAdmin
          .from('trustee_tokens')
          .insert({
            trustee_id: inviteId,
            token: secureToken,
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days expiry
          });

        if (retryError) {
          console.error("Error on retry storing trustee token:", retryError);
          // Continue anyway, as we can still use the basic link
        }
      }
    } catch (tokenStoreError) {
      console.error("Exception storing trustee token:", tokenStoreError);
      // Continue anyway, as we can still use the basic link
    }

    // Generate a unique invitation link with trustee ID, email, and token
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://legalock.com';
    const invitationLink = `${appUrl}/trustee/accept?id=${inviteId}&email=${encodeURIComponent(trusteeEmail)}&token=${secureToken}`;

    // Use default values if data is missing
    const safeInviterName = inviterName || trusteeData.inviter_first_name || 'A Legalock User';
    const safeInviterEmail = inviterEmail || trusteeData.inviter_email || '<EMAIL>';
    const safeTrusteeName = trusteeName || `${trusteeData.first_name} ${trusteeData.last_name}`.trim() || 'Trustee';

    // Call the Supabase function to send the email
    try {
      console.log('Sending invitation email to:', trusteeEmail, 'from:', inviterEmail);

      const functionResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/send-trustee-invitation`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          },
          body: JSON.stringify({
            inviterName: safeInviterName,
            inviterEmail: safeInviterEmail,
            trusteeName: safeTrusteeName,
            trusteeEmail,
            permissions,
            inviteId,
            message: message || '',
            magicLink: invitationLink
          }),
        }
      );

      if (!functionResponse.ok) {
        const errorData = await functionResponse.json();
        console.error('Error from Supabase function:', errorData);
        return NextResponse.json(
          { error: 'Failed to send invitation email' },
          { status: 500 }
        );
      }

      console.log('Email sent successfully via Supabase function');
    } catch (emailError) {
      console.error('Error calling Supabase function:', emailError);
      return NextResponse.json(
        { error: 'Failed to send invitation email' },
        { status: 500 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      console.error('Failed to get or create auth user mapping');
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // Update the trustee record to mark the invitation as sent
    // Use our new database function that bypasses RLS
    try {
      const { data: updateResult, error: updateError } = await supabaseAdmin.rpc('update_trustee_invitation', {
        p_trustee_id: inviteId,
        p_current_time: new Date().toISOString()
      });

      if (updateError) {
        console.error('Error updating trustee record with function:', updateError);

        // Try a direct update as a fallback
        try {
          const { error: directError } = await supabaseAdmin
            .from('trustees')
            .update({
              invitation_sent_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', inviteId);

          if (directError) {
            console.error('Error with direct update fallback:', directError);
            // Log but continue - the invitation was sent even if we couldn't update the record
          }
        } catch (directError) {
          console.error('Exception during direct update fallback:', directError);
          // Continue anyway since the email was sent
        }
      }
    } catch (error) {
      console.error('Exception updating trustee record:', error);
      // Continue anyway since the email was sent
    }

    // Log the invitation in our operations log
    try {
      await supabaseAdmin
        .from('trustee_operations_log')
        .insert({
          operation: 'INVITE',
          trustee_id: inviteId,
          performed_by: authUserId,
          details: {
            inviter_email: safeInviterEmail,
            trustee_email: trusteeEmail,
            timestamp: new Date().toISOString()
          }
        });
    } catch (logError) {
      console.error('Error logging trustee invitation:', logError);
      // Continue anyway
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error sending trustee invitation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to send invitation' },
      { status: 500 }
    );
  }
}
