import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Fetch all data in parallel
    const [
      assetsResult,
      documentsResult,
      trusteesResult,
      contactsResult,
      servicesResult,
      lastWishesResult,
      willProgressResult,
      timeCapsuleResult
    ] = await Promise.all([
      // Assets
      supabaseAdmin
        .from('assets')
        .select('*')
        .eq('user_id', user.id),

      // Recent documents
      supabaseAdmin
        .from('documents')
        .select('id, name, category, updated_at')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })
        .limit(3),

      // Trustees
      supabaseAdmin
        .from('trustees')
        .select('*')
        .eq('user_id', user.id),

      // Contacts
      supabaseAdmin
        .from('contacts')
        .select('*')
        .eq('user_id', user.id)
        .limit(3),

      // Services
      supabaseAdmin
        .from('service_sunset')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(3),

      // Last wishes
      supabaseAdmin
        .from('last_wishes')
        .select('*')
        .eq('user_id', user.id)
        .single(),

      // Will progress
      supabaseAdmin
        .from('will_progress')
        .select('*')
        .eq('user_id', user.id)
        .single(),

      // Time capsules
      supabaseAdmin
        .from('time_capsules')
        .select('*')
        .eq('user_id', user.id)
        .limit(3)
    ]);

    // Handle errors and prepare response
    const response: {
      assets: any[];
      documents: any[];
      trustees: any[];
      contacts: any[];
      services: any[];
      lastWishes: any;
      willProgress: any;
      timeCapsules: any[];
      autoRenewalCount?: number;
    } = {
      assets: assetsResult.error ? [] : assetsResult.data || [],
      documents: documentsResult.error ? [] : documentsResult.data || [],
      trustees: trusteesResult.error ? [] : trusteesResult.data || [],
      contacts: contactsResult.error ? [] : contactsResult.data || [],
      services: servicesResult.error ? [] : servicesResult.data || [],
      lastWishes: lastWishesResult.error ?
        (lastWishesResult.error.code === 'PGRST116' ? {} : null) :
        lastWishesResult.data || {},
      willProgress: willProgressResult.error ?
        (willProgressResult.error.code === 'PGRST116' ? {} : null) :
        willProgressResult.data || {},
      timeCapsules: timeCapsuleResult.error ? [] : timeCapsuleResult.data || []
    };

    // Get auto-renewal count for services
    const autoRenewalResult = await supabaseAdmin
      .from('service_sunset')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('auto_renewal', true);

    response.autoRenewalCount = autoRenewalResult.count || 0;

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error in dashboard API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
