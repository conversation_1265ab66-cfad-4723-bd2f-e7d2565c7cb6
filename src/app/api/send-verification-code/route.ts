import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { Resend } from 'resend';
import { getVerificationEmailTemplate } from '@/emails/verification-email';
import { supabaseAdmin } from '@/lib/supabase-unified';

// Initialize Resend with API key
const resendApiKey = process.env.RESEND_API_KEY || '';
const resend = new Resend(resendApiKey);

// Function to generate a 6-digit OTP code
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Generate a 6-digit verification code
    const otp = generateOTP();
    console.log(`Generated OTP for ${email}: ${otp}`);

    // Calculate expiration time (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Store the verification code in our database using the admin client
    const { data: codeData, error: codeError } = await supabaseAdmin
      .from('verification_codes')
      .insert({
        email,
        code: otp,
        expires_at: expiresAt.toISOString(),
      })
      .select();

    if (codeError) {
      console.error('Error storing verification code:', codeError);
      return NextResponse.json(
        { error: codeError.message },
        { status: 500 }
      );
    }

    // Send our custom email with the OTP code
    try {
      // Send a custom email with Resend using our template
      const { data: emailData, error: emailError } = await resend.emails.send({
        from: 'Legalock <<EMAIL>>',
        to: [email],
        subject: 'Verify your Legalock account',
        html: getVerificationEmailTemplate(email, otp),
      });

      if (emailError) {
        console.error('Error sending custom email with Resend:', emailError);
        return NextResponse.json(
          { error: emailError.message },
          { status: 500 }
        );
      } else {
        console.log('Custom verification email sent successfully');
      }
    } catch (resendError) {
      console.error('Exception sending custom email with Resend:', resendError);
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Unexpected error in send-verification-code:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
