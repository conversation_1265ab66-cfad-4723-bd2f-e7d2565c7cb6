import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');

    // Build the query
    let query = supabaseAdmin
      .from('documents')
      .select('*')
      .eq('user_id', user.id);

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    // Execute the query
    const { data, error } = await query.order('updated_at', { ascending: false });

    if (error) {
      // If the table doesn't exist, return an empty array
      if (error.code === '42P01') { // undefined_table
        return NextResponse.json([]);
      }

      console.error('Error fetching documents:', error);
      return NextResponse.json(
        { error: 'Failed to fetch documents' },
        { status: 500 }
      );
    }

    return NextResponse.json(data || []);
  } catch (error: any) {
    console.error('Error in documents API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Add the user_id to the document data
    const documentData = {
      ...body,
      user_id: user.id
    };

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();



    // Insert the document
    const { data, error } = await supabaseAdmin
      .from('documents')
      .insert(documentData)
      .select();

    if (error) {
      console.error('Error creating document:', error);
      return NextResponse.json(
        { error: `Failed to create document: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data[0]);
  } catch (error: any) {
    console.error('Error in documents API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
