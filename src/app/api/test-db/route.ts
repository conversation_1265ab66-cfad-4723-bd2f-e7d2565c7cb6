import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/auth-utils-conditional';

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase URL and service key are set
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Database configuration error: Missing Supabase credentials',
        env: {
          supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
          supabaseServiceKey: supabaseServiceKey ? 'Set' : 'Not set',
        }
      }, { status: 500 });
    }

    // Test database connection by querying a simple table
    console.log(`Testing database connection to ${supabaseUrl.substring(0, 20)}...`);
    
    // First, try a simple health check
    const { data: healthData, error: healthError } = await supabaseAdmin.rpc('get_service_role');
    
    if (healthError) {
      console.error('Database health check failed:', healthError);
      return NextResponse.json({
        success: false,
        error: `Database health check failed: ${healthError.message}`,
        details: healthError
      }, { status: 500 });
    }
    
    // Now try to query the custom_users table
    const { data, error } = await supabaseAdmin
      .from('custom_users')
      .select('count(*)')
      .limit(1);

    if (error) {
      console.error('Database query failed:', error);
      return NextResponse.json({
        success: false,
        error: `Database query failed: ${error.message}`,
        details: error
      }, { status: 500 });
    }

    // Success
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: {
        userCount: data,
        healthCheck: healthData
      }
    });
  } catch (error: any) {
    console.error('Error in test-db API:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'An error occurred while testing database connection',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}
