import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if the user exists in auth.users
    let exists = false;

    try {
      // List all users and filter by email
      const { data: listData, error: listError } = await supabaseAdmin.auth.admin.listUsers();

      if (listError) {
        console.error('Error listing users:', listError);
      }

      if (listData && listData.users) {
        // Check if any user has the matching email
        const matchingUser = listData.users.find((user: any) =>
          user && user.email && user.email.toLowerCase() === email.toLowerCase()
        );

        exists = !!matchingUser;
        console.log(`User existence check for ${email}: ${exists ? 'Found' : 'Not found'}`);
      }
    } catch (listErr) {
      console.error('Error listing users:', listErr);
    }

    // We've already handled errors in the try/catch blocks above
    // and set the exists variable appropriately

    return NextResponse.json({ exists });
  } catch (error: any) {
    console.error('Error in check-user-exists API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
