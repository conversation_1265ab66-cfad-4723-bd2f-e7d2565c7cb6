import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { deleteSession } from '@/lib/auth-utils-conditional';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (sessionToken) {
      // Delete the session
      await deleteSession(sessionToken);

      // Clear the session cookie
      cookieStore.delete('session_token');
    }

    return NextResponse.json({
      success: true,
      message: 'Signed out successfully',
    });
  } catch (error: any) {
    console.error('Error in signout:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred during signout' },
      { status: 500 }
    );
  }
}
