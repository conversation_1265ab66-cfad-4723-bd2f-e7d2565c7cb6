import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  createUser,
  generateVerificationCode,
  getUserByEmail,
  sendVerificationEmail,
  storeVerificationCode,
  markEmailAsVerified,
  createSession,
  createUserProfile,
  supabaseAdmin
} from '@/lib/auth-utils-conditional';

// Route segment config
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { email, password, firstName, lastName, autoVerify = false, trusteeInvitationId, tier = 'free' } = await request.json();

    // Validate input
    if (!email || !password || !firstName || !lastName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already registered' },
        { status: 409 }
      );
    }

    // Create the user with the selected tier
    const user = await createUser(email, password, firstName, lastName, tier);

    // If this is a trustee invitation registration, handle it
    if (trusteeInvitationId) {
      // Mark the email as verified
      await markEmailAsVerified(email);

      // Create a profile for the user if it doesn't exist yet
      try {
        const { data: profileData, error: profileError } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .single();

        if (profileError && profileError.code === 'PGRST116') {
          // Profile doesn't exist, create it with the selected tier
          await createUserProfile(user.id, user.email, firstName, lastName, tier);
        }
      } catch (error) {
        console.error('Error checking/creating profile:', error);
        // Continue anyway, as this is not critical
      }

      // Create a session
      const session = await createSession(user.id);

      // Set the session cookie
      const cookieStore = await cookies();
      cookieStore.set('session_token', session.sessionToken, {
        expires: session.expiresAt,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        path: '/',
        sameSite: 'lax',
      });

      // Update the trustee record to link it to this user
      try {
        console.log(`[signup] Linking trustee invitation ${trusteeInvitationId} to user ${user.id} with email ${email}`);

        // First, verify the trustee record exists
        const { data: trusteeData, error: fetchError } = await supabaseAdmin
          .from('trustees')
          .select('*')
          .eq('id', trusteeInvitationId)
          .maybeSingle();

        if (fetchError) {
          console.error('[signup] Error fetching trustee record:', fetchError);
          // Continue anyway, as the user account is created
        } else if (!trusteeData) {
          console.error('[signup] Trustee record not found for ID:', trusteeInvitationId);
          // Continue anyway, as the user account is created
        } else {
          console.log('[signup] Found trustee record:', {
            id: trusteeData.id,
            email: trusteeData.trustee_email,
            status: trusteeData.status,
            inviterId: trusteeData.user_id
          });

          // Verify the email matches the invitation
          if (trusteeData.trustee_email.toLowerCase() !== email.toLowerCase()) {
            console.warn(`[signup] Email mismatch for trustee invitation: expected ${trusteeData.trustee_email}, got ${email}`);
            // We'll still proceed, but log the mismatch
          }

          // We no longer allow name updates during trustee signup
          // The name from the invitation will be preserved
          const nameUpdated = false;
          console.log(`[signup] Preserving trustee name from invitation: "${trusteeData.first_name} ${trusteeData.last_name}"`);

          // Use the name from the invitation instead of the submitted name
          // This ensures the name is preserved throughout the process
          const finalFirstName = trusteeData.first_name;
          const finalLastName = trusteeData.last_name;

          // Update the user's name to match the invitation
          try {
            console.log(`[signup] Updating user name to match invitation: ${finalFirstName} ${finalLastName}`);
            await supabaseAdmin.auth.admin.updateUserById(user.id, {
              user_metadata: {
                first_name: finalFirstName,
                last_name: finalLastName
              }
            });
          } catch (nameError) {
            console.error('[signup] Error updating user name:', nameError);
            // Continue anyway, as this is not critical
          }

          // Update the trustee record to link it to the user, but don't activate it yet
          // The activation will happen through the TrusteeInvitationChecker component
          // after the user has accepted the terms
          const updateData: any = {
            trustee_user_id: user.id,
            status: 'pending_auth', // Keep as pending until terms are accepted
          };

          // We're not updating the name as we're preserving the original invitation name

          console.log('[signup] Updating trustee record with data:', {
            ...updateData,
            id: trusteeInvitationId
          });

          const { error: trusteeError } = await supabaseAdmin
            .from('trustees')
            .update(updateData)
            .eq('id', trusteeInvitationId);

          if (trusteeError) {
            console.error('[signup] Error updating trustee record:', trusteeError);
            // Continue anyway, as the user account is created
          } else {
            console.log(`[signup] Successfully linked trustee invitation ${trusteeInvitationId} to user ${user.id}`);

            // Verify the update was successful
            const { data: verifyData, error: verifyError } = await supabaseAdmin
              .from('trustees')
              .select('trustee_user_id, status')
              .eq('id', trusteeInvitationId)
              .single();

            if (verifyError) {
              console.error('[signup] Error verifying trustee update:', verifyError);
            } else if (!verifyData) {
              console.error('[signup] No verification data returned for trustee update');
            } else {
              console.log('[signup] Verified trustee update:', {
                trusteeId: trusteeInvitationId,
                linkedUserId: verifyData.trustee_user_id,
                status: verifyData.status
              });
            }
          }
        }
      } catch (trusteeUpdateError) {
        console.error('[signup] Error updating trustee record:', trusteeUpdateError);
        // Continue anyway, as the user account is created
      }

      // Also check for any other pending invitations for this email
      try {
        console.log(`[signup] Checking for other pending trustee invitations for email: ${email}`);
        const { data: otherInvitations, error: otherError } = await supabaseAdmin
          .from('trustees')
          .select('id, status')
          .eq('trustee_email', email.toLowerCase())
          .in('status', ['pending', 'pending_auth'])
          .is('trustee_user_id', null);

        if (otherError) {
          console.error('[signup] Error checking for other pending trustee invitations:', otherError);
        } else if (otherInvitations && otherInvitations.length > 0) {
          console.log(`[signup] Found ${otherInvitations.length} other pending trustee invitations for ${email}`);

          // Update all pending invitations to link them to this user
          for (const invitation of otherInvitations) {
            // Skip the one we just processed
            if (invitation.id === trusteeInvitationId) continue;

            console.log(`[signup] Processing other invitation: ${invitation.id}`);

            // Get the full trustee record to check if we need to update the name
            const { data: invitationData, error: invitationError } = await supabaseAdmin
              .from('trustees')
              .select('*')
              .eq('id', invitation.id)
              .maybeSingle();

            if (invitationError) {
              console.error(`[signup] Error fetching invitation ${invitation.id}:`, invitationError);
              continue; // Skip this invitation
            }

            if (!invitationData) {
              console.error(`[signup] Invitation not found: ${invitation.id}`);
              continue; // Skip this invitation
            }

            // We no longer allow name updates during trustee signup
            // The name from the invitation will be preserved
            const nameUpdated = false;
            console.log(`[signup] Preserving trustee name for invitation ${invitation.id}: "${invitationData.first_name} ${invitationData.last_name}"`);

            // Use the name from the invitation instead of the submitted name

            // Prepare update data
            const updateData: any = {
              trustee_user_id: user.id,
              status: 'pending_auth', // Keep as pending_auth until terms are accepted
            };

            // We're not updating the name as we're preserving the original invitation name

            console.log(`[signup] Updating invitation ${invitation.id} with data:`, updateData);

            const { error: updateError } = await supabaseAdmin
              .from('trustees')
              .update(updateData)
              .eq('id', invitation.id);

            if (updateError) {
              console.error(`[signup] Error updating trustee record ${invitation.id}:`, updateError);
            } else {
              console.log(`[signup] Successfully linked trustee invitation ${invitation.id} to user ${user.id}`);
            }
          }
        } else {
          console.log(`[signup] No other pending trustee invitations found for ${email}`);
        }
      } catch (error) {
        console.error('[signup] Error handling other trustee invitations:', error);
        // Continue anyway, as this is not critical
      }

      // Return user data for auto-verified users with trustee flag
      return NextResponse.json({
        success: true,
        message: 'User created and verified successfully.',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          emailVerified: true,
        },
        isTrusteeAccepted: true, // Flag to indicate this was a trustee acceptance
      });
    }

    // For regular sign-ups, generate and send verification code
    const verificationCode = generateVerificationCode();
    await storeVerificationCode(email, verificationCode);

    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationCode);
    if (!emailSent) {
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'User created successfully. Please check your email for verification code.',
      userId: user.id,
    });
  } catch (error: any) {
    console.error('Error in signup:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred during signup' },
      { status: 500 }
    );
  }
}
