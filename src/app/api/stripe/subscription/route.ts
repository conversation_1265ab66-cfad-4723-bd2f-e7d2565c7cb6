import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import stripe from '@/lib/stripe';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/auth-utils-conditional';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Try to get the user's profile from Supabase
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    // If there's an error or no profile, return free plan
    // This handles cases where the profiles table doesn't exist yet
    if (profileError || !profile || !profile.stripe_customer_id) {
      return NextResponse.json({
        plan: 'free',
        isSubscribed: false,
        subscription: null,
      });
    }

    // Get the customer's subscriptions from Stripe
    const subscriptions = await stripe.subscriptions.list({
      customer: profile.stripe_customer_id,
      status: 'active',
      expand: ['data.default_payment_method'],
    });

    // If no active subscriptions, return free plan
    if (subscriptions.data.length === 0) {
      return NextResponse.json({
        plan: 'free',
        isSubscribed: false,
        subscription: null,
      });
    }

    // Get the subscription details
    const subscription = subscriptions.data[0];
    const priceId = subscription.items.data[0].price.id;

    // Determine the plan based on the price ID
    let plan = 'free';
    if (priceId === process.env.STRIPE_PREMIUM_PLAN_ID) {
      plan = 'premium';
    }

    return NextResponse.json({
      plan,
      isSubscribed: true,
      subscription: {
        id: subscription.id,
        status: subscription.status,
        currentPeriodEnd: new Date((subscription as any).current_period_end * 1000).toISOString(),
        cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
      },
    });
  } catch (error: any) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching subscription' },
      { status: 500 }
    );
  }
}
