import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import stripe from '@/lib/stripe';
import Strip<PERSON> from 'stripe';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/auth-utils-conditional';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Try to get the user's profile from Supabase
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    // If there's an error or no profile, return an error
    if (profileError || !profile || !profile.stripe_customer_id || !profile.stripe_subscription_id) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      );
    }

    // Cancel the subscription at the end of the current period
    const subscriptionResponse = await stripe.subscriptions.update(
      profile.stripe_subscription_id,
      { cancel_at_period_end: true }
    );

    // Access the subscription data from the response
    const subscription = subscriptionResponse as Stripe.Subscription;

    // Ensure we have the correct type with the expected properties
    interface SubscriptionWithPeriodEnd extends Stripe.Subscription {
      current_period_end: number;
    }

    const typedSubscription = subscription as SubscriptionWithPeriodEnd;

    // Update the profile with the cancellation status
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: 'cancelling',
        subscription_end_date: new Date(typedSubscription.current_period_end * 1000).toISOString(),
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return NextResponse.json(
        { error: 'Failed to update subscription status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription will be cancelled at the end of the current billing period',
      subscription: {
        id: typedSubscription.id,
        status: typedSubscription.status,
        currentPeriodEnd: new Date(typedSubscription.current_period_end * 1000).toISOString(),
        cancelAtPeriodEnd: typedSubscription.cancel_at_period_end,
      },
    });
  } catch (error: any) {
    console.error('Error cancelling subscription:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}
