import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the trustee ID and user ID from the request body
    const { trusteeId, userId, email } = await request.json();

    if (!trusteeId || !userId) {
      return NextResponse.json(
        { error: 'Trustee ID and User ID are required', success: false },
        { status: 400 }
      );
    }

    console.log(`[final-activate] Last resort activation for invitation ${trusteeId} for user ${userId} with email ${email}`);

    // First, try using the RPC function
    try {
      console.log('[final-activate] Attempting to use force_update_trustee RPC function');
      const { data: rpcData, error: rpcError } = await supabaseAdmin.rpc('force_update_trustee', {
        p_trustee_id: trusteeId,
        p_user_id: userId,
        p_current_time: new Date().toISOString()
      });

      if (rpcError) {
        console.error('[final-activate] RPC function failed:', rpcError);
      } else {
        console.log('[final-activate] RPC function succeeded:', rpcData);

        // Verify the update
        const { data: verifyData, error: verifyError } = await supabaseAdmin
          .from('trustees')
          .select('status, trustee_user_id')
          .eq('id', trusteeId)
          .single();

        if (!verifyError && verifyData && verifyData.status === 'active' && verifyData.trustee_user_id === userId) {
          console.log('[final-activate] Verification successful after RPC update');
          return NextResponse.json({
            success: true,
            message: 'Trustee activated successfully via RPC'
          });
        } else {
          console.error('[final-activate] RPC update verification failed:', { verifyData, verifyError });
        }
      }
    } catch (rpcError) {
      console.error('[final-activate] Exception in RPC function:', rpcError);
    }

    // If RPC failed, try direct SQL
    try {
      console.log('[final-activate] Attempting direct SQL update');

      // Create a raw SQL query
      const query = `
        UPDATE trustees
        SET trustee_user_id = '${userId}',
            status = 'active',
            invitation_accepted_at = '${new Date().toISOString()}'
        WHERE id = '${trusteeId}'
      `;

      const { error: sqlError } = await supabaseAdmin.rpc('exec_sql', { sql: query });

      if (sqlError) {
        console.error('[final-activate] Direct SQL update failed:', sqlError);
      } else {
        console.log('[final-activate] Direct SQL update succeeded');

        // Verify the update
        const { data: verifyData, error: verifyError } = await supabaseAdmin
          .from('trustees')
          .select('status, trustee_user_id')
          .eq('id', trusteeId)
          .single();

        if (!verifyError && verifyData && verifyData.status === 'active' && verifyData.trustee_user_id === userId) {
          console.log('[final-activate] Verification successful after SQL update');
          return NextResponse.json({
            success: true,
            message: 'Trustee activated successfully via SQL'
          });
        } else {
          console.error('[final-activate] SQL update verification failed:', { verifyData, verifyError });
        }
      }
    } catch (sqlError) {
      console.error('[final-activate] Exception in SQL update:', sqlError);
    }

    // If all else fails, try a completely different approach
    try {
      console.log('[final-activate] Attempting to create a service role client');

      // Use the existing supabaseAdmin client
      const serviceClient = supabaseAdmin;

      // Try to update using the service role client
      const { error: serviceError } = await serviceClient
        .from('trustees')
        .update({
          trustee_user_id: userId,
          status: 'active',
          invitation_accepted_at: new Date().toISOString()
        })
        .eq('id', trusteeId);

      if (serviceError) {
        console.error('[final-activate] Service role update failed:', serviceError);
      } else {
        console.log('[final-activate] Service role update succeeded');

        // Verify the update
        const { data: verifyData, error: verifyError } = await serviceClient
          .from('trustees')
          .select('status, trustee_user_id')
          .eq('id', trusteeId)
          .single();

        if (!verifyError && verifyData && verifyData.status === 'active' && verifyData.trustee_user_id === userId) {
          console.log('[final-activate] Verification successful after service role update');
          return NextResponse.json({
            success: true,
            message: 'Trustee activated successfully via service role'
          });
        } else {
          console.error('[final-activate] Service role update verification failed:', { verifyData, verifyError });
        }
      }
    } catch (serviceError) {
      console.error('[final-activate] Exception in service role update:', serviceError);
    }

    // If we got here, all methods failed
    console.error('[final-activate] All activation methods failed');
    return NextResponse.json(
      { error: 'All activation methods failed', success: false },
      { status: 500 }
    );
  } catch (error: any) {
    console.error('[final-activate] Unhandled exception:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred', success: false },
      { status: 500 }
    );
  }
}
