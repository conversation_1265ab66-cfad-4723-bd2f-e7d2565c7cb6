import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the trustee ID and user ID from the request body
    const { trusteeId, userId } = await request.json();

    if (!trusteeId || !userId) {
      return NextResponse.json(
        { error: 'Trustee ID and User ID are required' },
        { status: 400 }
      );
    }

    console.log(`[basic-activate] Processing invitation ${trusteeId} for user ${userId}`);

    // Simple direct update - no complex logic, just update the record
    try {
      const initialUpdateData = {
        trustee_user_id: userId,
        status: 'active',
        invitation_accepted_at: new Date().toISOString(),
      };

      console.log('[basic-activate] Updating trustee record with data:', initialUpdateData);

      // First, try using the force_update_trustee RPC function which bypasses RLS
      try {
        console.log('[basic-activate] Attempting to use force_update_trustee RPC function');
        const { data: rpcData, error: rpcError } = await supabaseAdmin.rpc('force_update_trustee', {
          p_trustee_id: trusteeId,
          p_user_id: userId,
          p_current_time: new Date().toISOString()
        });

        if (rpcError) {
          console.error('[basic-activate] RPC function failed:', rpcError);
          // Fall back to direct update
        } else {
          console.log('[basic-activate] RPC function succeeded:', rpcData);
          // Verify the update
          const { data: verifyData, error: verifyError } = await supabaseAdmin
            .from('trustees')
            .select('status, trustee_user_id')
            .eq('id', trusteeId)
            .single();

          if (!verifyError && verifyData && verifyData.status === 'active' && verifyData.trustee_user_id === userId) {
            console.log('[basic-activate] Verification successful after RPC update');
            return NextResponse.json({
              success: true,
              message: 'Trustee activated successfully via RPC'
            });
          } else {
            console.error('[basic-activate] RPC update verification failed:', { verifyData, verifyError });
            // Continue to try direct update
          }
        }
      } catch (rpcError) {
        console.error('[basic-activate] Exception in RPC function:', rpcError);
        // Fall back to direct update
      }

      // Try direct update as fallback
      console.log('[basic-activate] Falling back to direct update');
      const updatePayload = {
        trustee_user_id: userId,
        status: 'active',
        invitation_accepted_at: new Date().toISOString(),
      };

      const { data: responseData, error: updateError } = await supabaseAdmin
        .from('trustees')
        .update(updatePayload)
        .eq('id', trusteeId)
        .select();

      console.log('[basic-activate] Direct update response:', { data: responseData, error: updateError });

      if (updateError) {
        console.error('[basic-activate] Error updating trustee record:', updateError);
        return NextResponse.json(
          { error: `Failed to update trustee: ${updateError.message}` },
          { status: 500 }
        );
      }

      // Verify the update
      const { data: verifyData, error: verifyError } = await supabaseAdmin
        .from('trustees')
        .select('status, trustee_user_id')
        .eq('id', trusteeId)
        .single();

      if (verifyError || !verifyData || verifyData.status !== 'active' || verifyData.trustee_user_id !== userId) {
        console.error('[basic-activate] Update verification failed:', { verifyData, verifyError });
        return NextResponse.json(
          { error: 'Update appeared to succeed but verification failed' },
          { status: 500 }
        );
      }

      console.log('[basic-activate] Successfully activated trustee:', trusteeId);

      return NextResponse.json({
        success: true,
        message: 'Trustee activated successfully'
      });
    } catch (error: any) {
      console.error('[basic-activate] Exception updating trustee record:', error);
      return NextResponse.json(
        { error: `Exception updating trustee: ${error.message || 'Unknown error'}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('[basic-activate] Unhandled exception in main handler:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
