import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the trustee ID and user ID from the request body
    const { trusteeId, userId } = await request.json();

    if (!trusteeId || !userId) {
      return NextResponse.json(
        { error: 'Trustee ID and User ID are required' },
        { status: 400 }
      );
    }

    console.log(`[force-activate] Processing invitation ${trusteeId} for user ${userId}`);

    // Execute direct SQL to update the trustee record
    try {
      // Use the PostgreSQL function that bypasses RLS
      const { data, error } = await supabaseAdmin.rpc('force_update_trustee', {
        p_trustee_id: trusteeId,
        p_user_id: userId,
        p_current_time: new Date().toISOString()
      });

      if (error) {
        console.error('[force-activate] Error executing force_update_trustee:', error);
        
        // Try a direct SQL query as a last resort
        try {
          console.log('[force-activate] Attempting direct SQL query');
          
          // Execute a direct SQL query
          const { data: sqlData, error: sqlError } = await supabaseAdmin.from('trustees').update({
            trustee_user_id: userId,
            status: 'active',
            invitation_accepted_at: new Date().toISOString()
          })
          .eq('id', trusteeId)
          .select();
          
          if (sqlError) {
            console.error('[force-activate] Direct SQL query failed:', sqlError);
            return NextResponse.json(
              { error: `Failed to update trustee: ${sqlError.message}` },
              { status: 500 }
            );
          }
          
          console.log('[force-activate] Direct SQL query succeeded:', sqlData);
        } catch (sqlError) {
          console.error('[force-activate] Exception in direct SQL query:', sqlError);
          return NextResponse.json(
            { error: `Exception in direct SQL query: ${sqlError}` },
            { status: 500 }
          );
        }
      } else {
        console.log('[force-activate] force_update_trustee succeeded:', data);
      }
      
      // Verify the update
      const { data: verifyData, error: verifyError } = await supabaseAdmin
        .from('trustees')
        .select('status, trustee_user_id')
        .eq('id', trusteeId)
        .single();
        
      if (verifyError) {
        console.error('[force-activate] Error verifying update:', verifyError);
        return NextResponse.json(
          { error: `Failed to verify update: ${verifyError.message}` },
          { status: 500 }
        );
      }
      
      if (!verifyData || verifyData.status !== 'active' || verifyData.trustee_user_id !== userId) {
        console.error('[force-activate] Verification failed:', verifyData);
        return NextResponse.json(
          { error: 'Update verification failed' },
          { status: 500 }
        );
      }
      
      console.log('[force-activate] Successfully activated trustee:', trusteeId);
      
      return NextResponse.json({
        success: true,
        message: 'Trustee activated successfully'
      });
    } catch (error: any) {
      console.error('[force-activate] Exception updating trustee record:', error);
      return NextResponse.json(
        { error: `Exception updating trustee: ${error.message || 'Unknown error'}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('[force-activate] Unhandled exception in main handler:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
