import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    const { sessionToken, termsAccepted, used } = await request.json();

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Session token is required' },
        { status: 400 }
      );
    }

    console.log(`[update-session] Updating invitation session with token ${sessionToken.substring(0, 8)}...`);

    // Update the invitation session using the database function
    const { data, error } = await supabaseAdmin.rpc('update_invitation_session', {
      p_session_token: sessionToken,
      p_terms_accepted: termsAccepted,
      p_used: used
    });

    if (error) {
      console.error('[update-session] Error updating invitation session:', error);
      return NextResponse.json(
        { error: `Failed to update invitation session: ${error.message}` },
        { status: 500 }
      );
    }

    console.log('[update-session] Session update result:', data);

    // Return the update result
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('[update-session] Unhandled exception:', error);
    return NextResponse.json(
      { error: `Unhandled exception: ${error.message}` },
      { status: 500 }
    );
  }
}
