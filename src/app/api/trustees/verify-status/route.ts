import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the trustee ID and user ID from the request body
    const { trusteeId, userId, email } = await request.json();

    if (!trusteeId || !userId) {
      return NextResponse.json(
        { error: 'Trustee ID and User ID are required', isActive: false, reason: 'Missing parameters' },
        { status: 400 }
      );
    }

    console.log(`[verify-status] Checking status for invitation ${trusteeId} for user ${userId} with email ${email}`);

    // Use the admin client to bypass RLS
    const { data: trusteeData, error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('status, trustee_user_id, trustee_email')
      .eq('id', trusteeId)
      .single();

    if (fetchError) {
      console.error('[verify-status] Error fetching trustee record:', fetchError);
      return NextResponse.json(
        { isActive: false, reason: `Failed to fetch trustee: ${fetchError.message}` },
        { status: 200 } // Return 200 even for errors to allow client to handle it
      );
    }

    if (!trusteeData) {
      console.error('[verify-status] Trustee record not found');
      return NextResponse.json(
        { isActive: false, reason: 'Trustee record not found' },
        { status: 200 }
      );
    }

    console.log('[verify-status] Found trustee record:', trusteeData);

    // Check if the trustee is already active for this user
    const isActive = trusteeData.status === 'active' && trusteeData.trustee_user_id === userId;
    
    // Check if the email matches
    const emailMatches = !email || trusteeData.trustee_email === email;

    if (isActive) {
      console.log('[verify-status] Trustee is already active for this user');
      return NextResponse.json({
        isActive: true,
        status: trusteeData.status,
        emailMatches
      });
    } else {
      console.log('[verify-status] Trustee is not active for this user:', {
        currentStatus: trusteeData.status,
        currentUserId: trusteeData.trustee_user_id,
        expectedUserId: userId
      });
      
      return NextResponse.json({
        isActive: false,
        reason: trusteeData.status === 'active' 
          ? 'Trustee is active for a different user' 
          : `Trustee status is ${trusteeData.status}`,
        status: trusteeData.status,
        emailMatches
      });
    }
  } catch (error: any) {
    console.error('[verify-status] Unhandled exception:', error);
    return NextResponse.json(
      { isActive: false, reason: error.message || 'An error occurred' },
      { status: 200 } // Return 200 even for errors to allow client to handle it
    );
  }
}
