import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/supabase-unified';
import { ensureAuthUserMapping } from '@/lib/auth-mapping-utils';

/**
 * Unified trustee activation endpoint
 *
 * This endpoint consolidates all the previous activation methods into a single,
 * reliable endpoint that handles all trustee activation scenarios.
 */
export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // Get the trustee ID, email, and token from the request body
    const { trusteeId, email, token } = await request.json();

    if (!trusteeId) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    console.log(`[activate] Processing invitation ${trusteeId} for user ${user.id} (${user.email})`);

    // 1. Verify the trustee record exists
    const { data: trusteeData, error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('id', trusteeId)
      .single();

    if (fetchError || !trusteeData) {
      console.error('[activate] Error fetching trustee:', fetchError);
      return NextResponse.json(
        { error: 'Trustee not found' },
        { status: 404 }
      );
    }

    // 2. Verify email match if provided
    if (email && trusteeData.trustee_email.toLowerCase() !== email.toLowerCase()) {
      console.error('[activate] Email mismatch:', {
        providedEmail: email,
        trusteeEmail: trusteeData.trustee_email
      });
      return NextResponse.json(
        { error: 'Email mismatch. This invitation was sent to a different email address.' },
        { status: 403 }
      );
    }

    // 3. Verify token if provided
    if (token) {
      const { data: tokenData, error: tokenError } = await supabaseAdmin
        .from('trustee_tokens')
        .select('*')
        .eq('trustee_id', trusteeId)
        .eq('token', token)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (tokenError || !tokenData) {
        console.error('[activate] Invalid or expired token:', tokenError);
        return NextResponse.json(
          { error: 'Invalid or expired invitation link' },
          { status: 403 }
        );
      }
    }

    // 4. Check if already activated for this user
    if (trusteeData.status === 'active' && trusteeData.trustee_user_id === authUserId) {
      console.log('[activate] Trustee already activated for this user');
      return NextResponse.json({
        success: true,
        message: 'Trustee already activated',
        alreadyActivated: true
      });
    }

    // 5. Check if activated for a different user
    if (trusteeData.status === 'active' && trusteeData.trustee_user_id && trusteeData.trustee_user_id !== authUserId) {
      console.error('[activate] Trustee already activated for a different user');
      return NextResponse.json(
        { error: 'This invitation has already been accepted by another user' },
        { status: 403 }
      );
    }

    // 6. Activate the trustee using our new secure function
    try {
      // Use the new activate_trustee_safely function that bypasses RLS
      const { data: activateResult, error: activateError } = await supabaseAdmin.rpc('activate_trustee_safely', {
        p_trustee_id: trusteeId,
        p_user_id: authUserId
      });

      if (activateError) {
        console.error('[activate] Error with activate_trustee_safely function:', activateError);
        return NextResponse.json(
          { error: `Failed to activate trustee: ${activateError.message}` },
          { status: 500 }
        );
      }

      if (!activateResult.success) {
        console.error('[activate] Function reported failure:', activateResult);
        return NextResponse.json(
          { error: activateResult.error || 'Failed to activate trustee' },
          { status: 500 }
        );
      }

      // 7. Clean up the token if it exists
      if (token) {
        await supabaseAdmin
          .from('trustee_tokens')
          .delete()
          .eq('trustee_id', trusteeId)
          .eq('token', token);
      }

      // 8. Verify the activation was successful
      const { data: verifyData, error: verifyError } = await supabaseAdmin
        .from('trustees')
        .select('status, trustee_user_id, first_name, last_name')
        .eq('id', trusteeId)
        .single();

      if (verifyError || !verifyData) {
        console.error('[activate] Error verifying activation:', verifyError);
        return NextResponse.json(
          { error: 'Failed to verify activation' },
          { status: 500 }
        );
      }

      if (verifyData.status !== 'active' || verifyData.trustee_user_id !== authUserId) {
        console.error('[activate] Activation verification failed:', verifyData);
        return NextResponse.json(
          { error: 'Activation verification failed' },
          { status: 500 }
        );
      }

      // 9. Check for other pending invitations for this email
      const { data: pendingInvitations, error: pendingError } = await supabaseAdmin
        .from('trustees')
        .select('id')
        .eq('trustee_email', trusteeData.trustee_email)
        .eq('status', 'pending')
        .neq('id', trusteeId);

      if (!pendingError && pendingInvitations && pendingInvitations.length > 0) {
        console.log(`[activate] Found ${pendingInvitations.length} other pending invitations for this email`);

        // Activate all other pending invitations for this email
        for (const invitation of pendingInvitations) {
          await supabaseAdmin.rpc('activate_trustee', {
            p_trustee_id: invitation.id,
            p_user_id: authUserId
          }).catch(err => {
            console.error(`[activate] Error activating additional invitation ${invitation.id}:`, err);
          });
        }
      }

      // Get the user's current name for response
      let firstName = '';
      let lastName = '';
      let nameUpdated = false;

      // Try to get the user's name from auth.users
      try {
        const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(authUserId);

        if (!authError && authUser && authUser.user && authUser.user.user_metadata) {
          firstName = authUser.user.user_metadata.first_name || '';
          lastName = authUser.user.user_metadata.last_name || '';
        }
      } catch (error) {
        console.error('[activate] Error getting user name:', error);
      }

      // If we still don't have a name, use the email as a fallback
      if (!firstName && !lastName) {
        const emailName = user.email.split('@')[0];
        firstName = emailName;
        lastName = '';
      }

      // Check if the name is different from what was entered by the inviter
      if (firstName && verifyData &&
        (firstName !== verifyData.first_name || lastName !== verifyData.last_name)) {
        nameUpdated = true;
      }

      // Return success with name information
      return NextResponse.json({
        success: true,
        message: 'Trustee activated successfully',
        nameUpdated,
        originalName: nameUpdated && verifyData ? `${verifyData.first_name} ${verifyData.last_name}` : null,
        updatedName: nameUpdated ? `${firstName} ${lastName}` : null
      });
    } catch (error: any) {
      console.error('[activate] Exception during activation:', error);
      return NextResponse.json(
        { error: `Exception during activation: ${error.message || 'Unknown error'}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('[activate] Unhandled exception:', error);
    return NextResponse.json(
      { error: `Unhandled exception: ${error.message || 'Unknown error'}` },
      { status: 500 }
    );
  }
}
