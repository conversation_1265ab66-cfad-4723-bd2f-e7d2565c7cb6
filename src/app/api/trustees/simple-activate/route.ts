import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the trustee ID from the request body
    const { trusteeId } = await request.json();

    if (!trusteeId) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    console.log(`[simple-activate] Processing invitation ${trusteeId} for user ${user.id} (${user.email})`);

    // STEP 1: Direct SQL update to activate the trustee
    // This is the most reliable way to update the record
    try {
      // Use a direct SQL query to update the trustee record
      // This bypasses any potential issues with the Supabase client
      const { error } = await supabaseAdmin.rpc('activate_trustee', {
        p_trustee_id: trusteeId,
        p_user_id: user.id
      });

      if (error) {
        console.error('[simple-activate] Error calling activate_trustee RPC:', error);
        
        // Fallback to direct update if RPC fails
        console.log('[simple-activate] Falling back to direct update');
        
        const { error: updateError } = await supabaseAdmin
          .from('trustees')
          .update({
            trustee_user_id: user.id,
            status: 'active',
            invitation_accepted_at: new Date().toISOString()
          })
          .eq('id', trusteeId);
          
        if (updateError) {
          console.error('[simple-activate] Error with fallback update:', updateError);
          return NextResponse.json(
            { error: `Failed to activate trustee: ${updateError.message}` },
            { status: 500 }
          );
        }
      }
      
      console.log('[simple-activate] Successfully activated trustee:', trusteeId);
      
      return NextResponse.json({
        success: true,
        message: 'Trustee activated successfully'
      });
    } catch (error: any) {
      console.error('[simple-activate] Unhandled exception:', error);
      return NextResponse.json(
        { error: error.message || 'An error occurred' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('[simple-activate] Unhandled exception in main handler:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
