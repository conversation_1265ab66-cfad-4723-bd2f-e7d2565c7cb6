import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    const { trusteeId, email } = await request.json();

    if (!trusteeId || !email) {
      return NextResponse.json(
        { error: 'Trustee ID and email are required' },
        { status: 400 }
      );
    }

    console.log(`[create-session] Creating invitation session for trustee ${trusteeId} with email ${email}`);

    // Create an invitation session using the database function
    const { data, error } = await supabaseAdmin.rpc('create_invitation_session', {
      p_trustee_id: trusteeId,
      p_email: email,
      p_expiry_hours: 24 // Sessions expire after 24 hours
    });

    if (error) {
      console.error('[create-session] Error creating invitation session:', error);
      return NextResponse.json(
        { error: `Failed to create invitation session: ${error.message}` },
        { status: 500 }
      );
    }

    console.log('[create-session] Session created successfully:', data);

    // Return the session token and expiration
    return NextResponse.json({
      success: true,
      session_token: data.session_token,
      expires_at: data.expires_at
    });
  } catch (error: any) {
    console.error('[create-session] Unhandled exception:', error);
    return NextResponse.json(
      { error: `Unhandled exception: ${error.message}` },
      { status: 500 }
    );
  }
}
