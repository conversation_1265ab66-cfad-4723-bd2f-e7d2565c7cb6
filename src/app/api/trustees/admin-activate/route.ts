import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    // Get the trustee ID and user ID from the request body
    const { trusteeId, userId, email } = await request.json();

    if (!trusteeId || !userId) {
      return NextResponse.json(
        { error: 'Trustee ID and User ID are required' },
        { status: 400 }
      );
    }

    console.log(`[admin-activate] Processing invitation ${trusteeId} for user ${userId} with email ${email}`);

    // First, verify the trustee record exists
    const { data: trusteeData, error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('id', trusteeId)
      .single();

    if (fetchError) {
      console.error('[admin-activate] Error fetching trustee record:', fetchError);
      return NextResponse.json(
        { error: `Failed to fetch trustee: ${fetchError.message}` },
        { status: 500 }
      );
    }

    if (!trusteeData) {
      console.error('[admin-activate] Trustee record not found');
      return NextResponse.json(
        { error: 'Trustee record not found' },
        { status: 404 }
      );
    }

    console.log('[admin-activate] Found trustee record:', {
      id: trusteeData.id,
      email: trusteeData.trustee_email,
      status: trusteeData.status
    });

    // Verify the email matches
    if (email && trusteeData.trustee_email !== email) {
      console.error('[admin-activate] Email mismatch:', {
        providedEmail: email,
        trusteeEmail: trusteeData.trustee_email
      });
      return NextResponse.json(
        { error: 'Email mismatch' },
        { status: 403 }
      );
    }

    // Update the trustee record using the admin client
    const updateData = {
      trustee_user_id: userId,
      status: 'active',
      invitation_accepted_at: new Date().toISOString()
    };

    console.log('[admin-activate] Updating trustee record with data:', updateData);

    // Use the admin client to bypass RLS
    const { error: updateError } = await supabaseAdmin
      .from('trustees')
      .update(updateData)
      .eq('id', trusteeId);

    if (updateError) {
      console.error('[admin-activate] Error updating trustee record:', updateError);
      return NextResponse.json(
        { error: `Failed to update trustee: ${updateError.message}` },
        { status: 500 }
      );
    }

    // Verify the update was successful
    const { data: verifyData, error: verifyError } = await supabaseAdmin
      .from('trustees')
      .select('status, trustee_user_id')
      .eq('id', trusteeId)
      .single();

    if (verifyError) {
      console.error('[admin-activate] Error verifying update:', verifyError);
      return NextResponse.json(
        { error: `Failed to verify update: ${verifyError.message}` },
        { status: 500 }
      );
    }

    if (!verifyData || verifyData.status !== 'active' || verifyData.trustee_user_id !== userId) {
      console.error('[admin-activate] Verification failed:', verifyData);
      
      // Try one more direct SQL approach
      try {
        console.log('[admin-activate] Attempting direct SQL update as last resort');
        
        // Execute raw SQL using the admin client
        const { error: sqlError } = await supabaseAdmin.rpc('force_update_trustee', {
          p_trustee_id: trusteeId,
          p_user_id: userId,
          p_current_time: new Date().toISOString()
        });
        
        if (sqlError) {
          console.error('[admin-activate] Direct SQL update failed:', sqlError);
          return NextResponse.json(
            { error: `Direct SQL update failed: ${sqlError.message}` },
            { status: 500 }
          );
        }
        
        console.log('[admin-activate] Direct SQL update succeeded');
      } catch (sqlError) {
        console.error('[admin-activate] Exception in direct SQL update:', sqlError);
        return NextResponse.json(
          { error: 'All update methods failed' },
          { status: 500 }
        );
      }
    }

    console.log('[admin-activate] Successfully activated trustee:', trusteeId);
    
    return NextResponse.json({
      success: true,
      message: 'Trustee activated successfully'
    });
  } catch (error: any) {
    console.error('[admin-activate] Unhandled exception:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
