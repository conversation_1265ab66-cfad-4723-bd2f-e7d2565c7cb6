import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-unified';

export async function POST(request: NextRequest) {
  try {
    const { sessionToken } = await request.json();

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Session token is required' },
        { status: 400 }
      );
    }

    console.log(`[validate-session] Validating invitation session with token ${sessionToken.substring(0, 8)}...`);

    // Validate the invitation session using the database function
    const { data, error } = await supabaseAdmin.rpc('validate_invitation_session', {
      p_session_token: sessionToken
    });

    if (error) {
      console.error('[validate-session] Error validating invitation session:', error);
      return NextResponse.json(
        { error: `Failed to validate invitation session: ${error.message}` },
        { status: 500 }
      );
    }

    console.log('[validate-session] Session validation result:', data);

    // Return the validation result
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('[validate-session] Unhandled exception:', error);
    return NextResponse.json(
      { error: `Unhandled exception: ${error.message}` },
      { status: 500 }
    );
  }
}
