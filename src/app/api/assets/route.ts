import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById, supabaseAdmin } from '@/lib/auth-utils-conditional';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get assets for the user using supabaseAdmin
    const { data, error } = await supabaseAdmin
      .from('assets')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching assets:', error);
      return NextResponse.json(
        { error: 'Failed to fetch assets' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in assets API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Add the user_id to the asset data
    const assetData = {
      ...body,
      user_id: user.id
    };

    // Make sure the asset data is valid
    if (assetData.value !== undefined && isNaN(assetData.value)) {
      return NextResponse.json(
        { error: 'Asset value must be a valid number' },
        { status: 400 }
      );
    }

    // Log the asset data for debugging
    console.log('Asset data to insert:', JSON.stringify(assetData, null, 2));

    try {
      // Using supabaseAdmin imported from auth-utils

      // First, try to create a profile for this user to avoid foreign key issues
      // This is a workaround for the foreign key constraint issue
      const { error: profileError } = await supabaseAdmin
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email,
          subscription_tier: 'free',
          subscription_status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, { onConflict: 'id' });

      if (profileError) {
        console.error('Error creating profile:', profileError);
        // Continue anyway, as we'll try to insert the asset
      }

      // Now try to insert the asset again
      const { data, error: insertError } = await supabaseAdmin
        .from('assets')
        .insert(assetData)
        .select();

      if (insertError) {
        console.error('Error creating asset:', insertError);
        return NextResponse.json(
          { error: `Failed to create asset: ${insertError.message}` },
          { status: 500 }
        );
      }

      if (!data || data.length === 0) {
        return NextResponse.json(
          { error: 'No data returned after asset creation' },
          { status: 500 }
        );
      }

      return NextResponse.json(data[0]);
    } catch (insertError: any) {
      console.error('Exception creating asset:', insertError);
      return NextResponse.json(
        { error: `Exception creating asset: ${insertError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error in assets API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { id, ...assetData } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Make sure the asset data is valid
    if (assetData.value !== undefined && isNaN(assetData.value)) {
      return NextResponse.json(
        { error: 'Asset value must be a valid number' },
        { status: 400 }
      );
    }

    // Log the asset data for debugging
    console.log('Asset data to update:', JSON.stringify(assetData, null, 2));

    // Using supabaseAdmin imported from auth-utils

    // First, verify that the asset belongs to the user
    const { data: existingAsset, error: fetchError } = await supabaseAdmin
      .from('assets')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching asset:', fetchError);
      return NextResponse.json(
        { error: 'Asset not found or you do not have permission to update it' },
        { status: 404 }
      );
    }

    // Update the asset
    const { data, error: updateError } = await supabaseAdmin
      .from('assets')
      .update({
        ...assetData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating asset:', updateError);
      return NextResponse.json(
        { error: `Failed to update asset: ${updateError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in assets API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the asset ID from the URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Asset ID is required' },
        { status: 400 }
      );
    }

    // Using supabaseAdmin imported from auth-utils

    // First, verify that the asset belongs to the user
    const { data: existingAsset, error: fetchError } = await supabaseAdmin
      .from('assets')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching asset:', fetchError);
      return NextResponse.json(
        { error: 'Asset not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    // Delete the asset
    const { error: deleteError } = await supabaseAdmin
      .from('assets')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (deleteError) {
      console.error('Error deleting asset:', deleteError);
      return NextResponse.json(
        { error: `Failed to delete asset: ${deleteError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in assets API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}