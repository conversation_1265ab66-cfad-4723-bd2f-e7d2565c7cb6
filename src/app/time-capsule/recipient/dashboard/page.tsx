"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase-unified';
import { TimeCapsule } from '@/types/database.types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, Mail, User, Calendar, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';

export default function RecipientDashboardPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [capsules, setCapsules] = useState<TimeCapsule[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if the user has an email stored in localStorage
  useEffect(() => {
    const storedEmail = localStorage.getItem('recipient_email');
    if (storedEmail) {
      setEmail(storedEmail);
      setIsAuthenticated(true);
      fetchTimeCapsules(storedEmail);
    }
  }, []);

  const fetchTimeCapsules = async (recipientEmail: string) => {
    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('time_capsules')
        .select('*')
        .eq('recipient_email', recipientEmail)
        .eq('status', 'delivered')
        .order('delivery_date', { ascending: false });

      if (error) throw error;

      setCapsules(data || []);
    } catch (error) {
      console.error('Error fetching time capsules:', error);
      toast.error('Failed to load time capsules');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    setIsSubmitting(true);

    try {
      // Store the email in localStorage
      localStorage.setItem('recipient_email', email);

      // Fetch time capsules for this email
      await fetchTimeCapsules(email);

      setIsAuthenticated(true);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to authenticate');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('recipient_email');
    setEmail('');
    setIsAuthenticated(false);
    setCapsules([]);
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-12 px-4">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Time Capsule Recipient Login</CardTitle>
            <CardDescription>
              Enter your email address to view time capsules that have been sent to you.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? 'Loading...' : 'View My Time Capsules'}
              </Button>
            </form>
          </CardContent>

          <div className="mt-6 pt-6 border-t text-center">
            <p className="text-sm text-gray-500 mb-4">
              First time receiving a time capsule? Create an account to securely view all capsules sent to you.
            </p>
            <Button variant="outline" asChild className="w-full">
              <Link href="/time-capsule/recipient/register">
                Create Recipient Account
              </Link>
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-12 px-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Your Time Capsules</h1>
          <p className="text-gray-500">
            Viewing time capsules sent to {email}
          </p>
        </div>
        <Button variant="outline" onClick={handleLogout}>
          Sign Out
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48 mb-2" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : capsules.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No Time Capsules Found</CardTitle>
            <CardDescription>
              You don't have any time capsules delivered to this email address yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500">
              When someone sends you a time capsule, it will appear here after its scheduled delivery date.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {capsules.map((capsule) => (
            <Card key={capsule.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{capsule.title}</CardTitle>
                    <CardDescription>
                      From: {capsule.sender_name || 'Someone who cares about you'}
                    </CardDescription>
                  </div>
                  <div className="bg-blue-100 rounded-full p-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center text-sm text-gray-500 mb-4">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Delivered on {format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}</span>
                </div>

                <p className="line-clamp-2 text-gray-700">
                  {capsule.message || 'This time capsule contains a message for you.'}
                </p>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button asChild>
                  <Link href={`/time-capsule/view/${capsule.id}?code=${capsule.access_code}`}>
                    View Time Capsule
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
