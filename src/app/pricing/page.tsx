"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';
import { Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { formatPrice, getSubscriptionPlanDetails, SUBSCRIPTION_PLANS } from '@/lib/stripe-utils';
import { toast } from 'sonner';

export default function PricingPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { plan: currentPlan, isSubscribed, createCheckout } = useSubscription();
  const [isLoading, setIsLoading] = useState(false);

  const freePlan = getSubscriptionPlanDetails('free');
  const premiumPlan = getSubscriptionPlanDetails(SUBSCRIPTION_PLANS.PREMIUM);

  const handleSubscribe = async (planId: string) => {
    if (!user) {
      toast.info('Please sign in to subscribe');
      router.push('/login?redirect=/pricing');
      return;
    }

    setIsLoading(true);
    try {
      // First check if the user is already authenticated
      const response = await fetch('/api/auth/session');
      const sessionData = await response.json();

      if (!sessionData.authenticated) {
        toast.error('You must be logged in to subscribe');
        router.push('/login?redirect=/pricing');
        return;
      }

      // Now create the checkout
      const checkoutUrl = await createCheckout('premium');
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to create checkout session');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-16">
        <h1 className="text-4xl font-bold mb-4">Simple, Transparent Pricing</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Choose the plan that works best for you and your family's legacy planning needs.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {/* Free Plan */}
        <Card className="relative overflow-hidden border-2 border-gray-200 transition-all duration-300 hover:shadow-md">
          <CardHeader className="pb-8">
            <CardTitle className="text-2xl">Essential Legacy</CardTitle>
            <CardDescription>Basic features for everyone</CardDescription>
            <div className="mt-4">
              <span className="text-4xl font-bold">Free</span>
              <span className="text-gray-500 ml-2">forever</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="space-y-3">
              {freePlan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                if (user) {
                  // User is already on free plan
                  return;
                } else {
                  // Redirect to signup with free tier
                  router.push('/register?tier=free');
                }
              }}
              disabled={user && currentPlan === 'free'}
            >
              {user && currentPlan === 'free' ? 'Current Plan' : 'Get Started Free'}
            </Button>
          </CardFooter>
        </Card>

        {/* Premium Plan */}
        <Card className="relative overflow-hidden border-2 border-primary transition-all duration-300 hover:shadow-md">
          <div className="absolute top-0 right-0 bg-primary text-white px-3 py-1 text-sm font-medium">
            Recommended
          </div>
          <CardHeader className="pb-8">
            <CardTitle className="text-2xl">Legacy Preserver</CardTitle>
            <CardDescription>Advanced features for complete peace of mind</CardDescription>
            <div className="mt-4">
              <span className="text-4xl font-bold">{formatPrice(premiumPlan.price)}</span>
              <span className="text-gray-500 ml-2">/ year</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="space-y-3">
              {premiumPlan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full"
              onClick={() => {
                if (user) {
                  // User is logged in, handle subscription
                  handleSubscribe(SUBSCRIPTION_PLANS.PREMIUM);
                } else {
                  // User is not logged in, redirect to signup with premium tier
                  router.push('/register?tier=premium');
                }
              }}
              disabled={isLoading || (isSubscribed && currentPlan === 'premium')}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : isSubscribed && currentPlan === 'premium' ? (
                'Current Plan'
              ) : user ? (
                'Subscribe'
              ) : (
                'Get Started Premium'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="text-center mt-16 text-gray-600">
        <p className="mb-2">All plans include:</p>
        <ul className="space-y-1">
          <li>Secure data storage</li>
          <li>Regular updates and improvements</li>
          <li>Email support</li>
        </ul>
      </div>
    </div>
  );
}
