"use client";

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { LastWishes } from '@/types/database.types';
import {
  Heart,
  ArrowLeft,
  Leaf,
  PawPrint,
  Users,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

export default function TrusteeLastWishesPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.userId as string;
  const { user, loading } = useAuth();

  const [lastWishes, setLastWishes] = useState<LastWishes | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('');
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user && userId) {
      checkAccess();
    }
  }, [user, loading, userId, router]);

  const checkAccess = async () => {
    try {
      // Check if the current user is a trustee for the specified user
      const { data: trusteeData, error: trusteeError } = await supabase
        .from('trustees')
        .select('*')
        .eq('user_id', userId)
        .eq('trustee_user_id', user?.id)
        .eq('status', 'active')
        .single();

      if (trusteeError || !trusteeData) {
        toast.error('You do not have access to this user\'s last wishes');
        router.push('/trustee/dashboard');
        return;
      }

      // Check if there's a verified death notification
      const { data: deathData, error: deathError } = await supabase
        .from('death_notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'verified')
        .single();

      if (deathError || !deathData) {
        toast.error('You cannot access this information until the user\'s death has been verified');
        router.push('/trustee/dashboard');
        return;
      }

      setHasAccess(true);
      fetchUserInfo();
      fetchLastWishes();
    } catch (error: any) {
      console.error('Error checking access:', error);
      toast.error('Failed to verify access permissions');
      router.push('/trustee/dashboard');
    }
  };

  const fetchUserInfo = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('first_name, last_name')
        .eq('id', userId)
        .single();

      if (error) throw error;

      if (data) {
        setUserName(`${data.first_name} ${data.last_name}`);
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  const fetchLastWishes = async () => {
    try {
      setIsLoading(true);

      // For trustees, we need to use the Supabase client directly
      // since the API route is designed for the user's own last wishes
      const { data, error } = await supabase
        .from('last_wishes')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No last wishes found
          return;
        }
        throw error;
      }

      setLastWishes(data);
    } catch (error: any) {
      console.error('Error fetching last wishes:', error);
      toast.error('Failed to load last wishes');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading || !hasAccess) {
    return (
      <div className="container mx-auto py-8 text-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-500">Checking access...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" asChild className="mb-6">
        <Link href="/trustee/dashboard">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Trustee Dashboard
        </Link>
      </Button>

      <div className="flex items-center mb-6">
        <Heart className="h-8 w-8 text-red-600 mr-3" />
        <div>
          <h1 className="text-3xl font-bold">{userName}'s Last Wishes</h1>
          <p className="text-gray-600">
            Final wishes and instructions
          </p>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-10">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-500">Loading last wishes...</p>
        </div>
      ) : !lastWishes ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
          <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Last Wishes Found</h3>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">
            {userName} did not document any last wishes.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            {lastWishes.funeral_wishes && (
              <Card>
                <CardHeader>
                  <CardTitle>Funeral & Memorial Preferences</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap">{lastWishes.funeral_wishes}</p>
                </CardContent>
              </Card>
            )}

            {(lastWishes.burial_wishes || lastWishes.burial_option) && (
              <Card>
                <CardHeader>
                  <CardTitle>Burial & Remains Preferences</CardTitle>
                </CardHeader>
                <CardContent>
                  {lastWishes.burial_option && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Preferred Option:</h3>
                      <p className="font-medium">
                        {lastWishes.burial_option === 'burial' && 'Traditional Burial'}
                        {lastWishes.burial_option === 'cremation' && 'Cremation'}
                        {lastWishes.burial_option === 'green_burial' && 'Green/Natural Burial'}
                        {lastWishes.burial_option === 'donation' && 'Body Donation to Science'}
                        {lastWishes.burial_option === 'other' && 'Other'}
                      </p>
                    </div>
                  )}
                  {lastWishes.burial_wishes && (
                    <div>
                      {lastWishes.burial_option && <h3 className="text-sm font-medium text-gray-500 mb-1">Additional Details:</h3>}
                      <p className="whitespace-pre-wrap">{lastWishes.burial_wishes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {lastWishes.personal_messages && lastWishes.show_personal_messages && (
              <Card>
                <CardHeader>
                  <CardTitle>Personal Messages</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap">{lastWishes.personal_messages}</p>
                </CardContent>
              </Card>
            )}

            {lastWishes.other_wishes && (
              <Card>
                <CardHeader>
                  <CardTitle>Other Wishes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap">{lastWishes.other_wishes}</p>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Leaf className="h-5 w-5 text-green-600 mr-2" />
                  Organ Donation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-4">
                  <Badge className={lastWishes.is_organ_donor ? 'bg-green-100 text-green-800 border-green-200' : 'bg-gray-100 text-gray-800 border-gray-200'}>
                    {lastWishes.is_organ_donor ? (
                      <><CheckCircle className="h-3 w-3 mr-1" /> Registered Organ Donor</>
                    ) : (
                      <><XCircle className="h-3 w-3 mr-1" /> Not an Organ Donor</>
                    )}
                  </Badge>
                </div>

                {lastWishes.is_organ_donor && (
                  <div className="space-y-3">
                    {lastWishes.organ_donor_country && (
                      <div>
                        <p className="text-sm text-gray-500">Registered Country:</p>
                        <p className="font-medium">
                          {lastWishes.organ_donor_country === 'USA' && 'United States'}
                          {lastWishes.organ_donor_country === 'CAN' && 'Canada'}
                          {lastWishes.organ_donor_country === 'GBR' && 'United Kingdom'}
                          {lastWishes.organ_donor_country === 'AUS' && 'Australia'}
                          {lastWishes.organ_donor_country === 'NZL' && 'New Zealand'}
                          {lastWishes.organ_donor_country === 'DEU' && 'Germany'}
                          {lastWishes.organ_donor_country === 'FRA' && 'France'}
                          {lastWishes.organ_donor_country === 'ESP' && 'Spain'}
                          {lastWishes.organ_donor_country === 'ITA' && 'Italy'}
                          {lastWishes.organ_donor_country === 'JPN' && 'Japan'}
                          {lastWishes.organ_donor_country === 'OTHER' && 'Other'}
                        </p>
                      </div>
                    )}

                    {lastWishes.organ_donor_country === 'USA' && lastWishes.organ_donor_state && (
                      <div>
                        <p className="text-sm text-gray-500">Registered State:</p>
                        <p className="font-medium">{lastWishes.organ_donor_state}</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {lastWishes.has_pets && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PawPrint className="h-5 w-5 text-amber-600 mr-2" />
                    Pet Care Instructions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {lastWishes.pet_care_instructions ? (
                    <p className="whitespace-pre-wrap">{lastWishes.pet_care_instructions}</p>
                  ) : (
                    <p className="text-gray-500 italic">No specific pet care instructions provided.</p>
                  )}
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 text-blue-600 mr-2" />
                  Trustee Responsibilities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-4 rounded-md">
                  <div className="flex items-start">
                    <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-blue-700">
                        As a trustee, you are responsible for carrying out {userName}'s last wishes to the best of your ability. Please respect their preferences and instructions.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
