"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { CheckCircle, XCircle, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import MainHeader from '@/components/Navigation/MainHeader';
import AuthHeader from '@/components/Navigation/AuthHeader';

export default function TrusteeCompletePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Wait for auth to load
    if (loading) return;

    // If user is not logged in, redirect to sign in
    if (!user) {
      router.push('/login?redirect=/trustee/complete');
      return;
    }

    console.log('User authenticated, checking for pending trustee invitations');

    // Get the trustee ID from the URL or local storage
    const trusteeId = searchParams.get('trusteeId') || localStorage.getItem('pendingTrusteeId');
    const trusteeEmail = localStorage.getItem('pendingTrusteeEmail');

    if (!trusteeId) {
      console.log('No pending trustee invitation found');
      setError('No pending trustee invitation found');
      return;
    }

    console.log('Found pending trustee invitation:', trusteeId);
    console.log('User details:', {
      userId: user.id,
      userEmail: user.email,
      trusteeEmail: trusteeEmail
    });

    // Verify that the user's email matches the trustee email if available
    if (trusteeEmail && user.email && trusteeEmail.toLowerCase() !== user.email.toLowerCase()) {
      console.warn(`Email mismatch: Invitation for ${trusteeEmail}, but user logged in with ${user.email}`);
      // We'll still proceed, but log the mismatch
    }

    // Complete the trustee invitation process
    completeTrusteeInvitation(trusteeId);
  }, [user, loading, searchParams]);

  const completeTrusteeInvitation = async (trusteeId: string) => {
    try {
      setIsProcessing(true);
      console.log('Completing trustee invitation process for ID:', trusteeId);

      // First, check if this trustee record exists and isn't already linked
      const { data: trusteeData, error: fetchError } = await supabase
        .from('trustees')
        .select('*')
        .eq('id', trusteeId)
        .single();

      if (fetchError) {
        console.error('Error fetching trustee record:', fetchError);
        throw new Error('Could not find the trustee invitation');
      }

      console.log('Trustee data found:', {
        id: trusteeData.id,
        email: trusteeData.trustee_email,
        status: trusteeData.status,
        currentUserId: trusteeData.trustee_user_id,
        inviterId: trusteeData.user_id
      });

      if (trusteeData.trustee_user_id && trusteeData.trustee_user_id !== user?.id) {
        console.error('Trustee already linked to a different user:', trusteeData.trustee_user_id);
        throw new Error('This invitation has already been accepted by another user');
      }

      console.log('Linking trustee to user account:', user?.id);

      // Link the trustee to the user's account
      const { error } = await supabase
        .from('trustees')
        .update({
          trustee_user_id: user?.id,
          status: 'active',
          invitation_accepted_at: new Date().toISOString(),
        })
        .eq('id', trusteeId);

      if (error) {
        console.error('Error updating trustee record:', error);
        throw error;
      }

      // Double-check that the update was successful
      const { data: updatedTrustee, error: verifyError } = await supabase
        .from('trustees')
        .select('trustee_user_id, status')
        .eq('id', trusteeId)
        .single();

      if (verifyError) {
        console.error('Error verifying trustee update:', verifyError);
      } else {
        console.log('Verified trustee update:', {
          trusteeId: trusteeId,
          linkedUserId: updatedTrustee.trustee_user_id,
          status: updatedTrustee.status
        });
      }

      console.log('Successfully linked trustee to user account');

      // Clear all pending trustee data from local storage
      localStorage.removeItem('pendingTrusteeId');
      localStorage.removeItem('pendingTrusteeEmail');
      localStorage.removeItem('pendingTrusteeAcceptedAt');

      setIsComplete(true);
      toast.success('You are now a trustee!');
    } catch (error: any) {
      console.error('Error completing trustee invitation:', error);
      setError(error.message || 'Failed to complete trustee invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-gray-50">
        {user ? <MainHeader /> : <AuthHeader />}
        <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="mx-auto w-full max-w-md text-center">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-500">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {user ? <MainHeader /> : <AuthHeader />}
      <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md">
          {isProcessing ? (
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-500">Completing your trustee invitation...</p>
            </div>
          ) : error ? (
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Error</CardTitle>
                <CardDescription>
                  {error}
                </CardDescription>
              </CardHeader>
              <CardFooter className="flex justify-center">
                <Button asChild>
                  <Link href="/">
                    Return to Home
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ) : isComplete ? (
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>Invitation Accepted</CardTitle>
                <CardDescription>
                  You have successfully accepted the trustee invitation.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500 mb-4">
                  As a trustee, you will be responsible for managing digital assets and documents in case of emergency or after the person's passing.
                </p>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button asChild>
                  <Link href="/dashboard">
                    Go to Dashboard <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ) : null}
        </div>
      </div>
    </div>
  );
}
