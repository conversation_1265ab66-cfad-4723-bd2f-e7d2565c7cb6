"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { Users, CheckCircle, XCircle, Info, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import AuthHeader from '@/components/Navigation/AuthHeader';

// Import the shared fallback component
import ClientFallback from '@/components/ClientFallback';

// Main component that uses searchParams
function TrusteeInvitationContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [invitation, setInvitation] = useState<any>(null);
  const [inviter, setInviter] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [isDeclining, setIsDeclining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [acceptTerms, setAcceptTerms] = useState(false);

  useEffect(() => {
    const inviteId = searchParams.get('id');
    const emailFromUrl = searchParams.get('email');
    const sessionToken = searchParams.get('session');

    if (!inviteId) {
      setError('Invalid invitation link');
      setIsLoading(false);
      return;
    }

    // If we have a session token, validate it
    if (sessionToken) {
      // Store the session token in a cookie instead of localStorage
      document.cookie = `trustee_session=${sessionToken}; path=/; max-age=86400; SameSite=Lax`;
      console.log('Stored trustee session token in cookie');
    }
    // If we don't have a session token but have an email, create a new session
    else if (emailFromUrl && inviteId) {
      createInvitationSession(inviteId, emailFromUrl);
    }

    fetchInvitation(inviteId);
  }, [searchParams]);

  // Function to create a new invitation session
  const createInvitationSession = async (trusteeId: string, email: string) => {
    try {
      console.log('Creating invitation session for trustee:', trusteeId, 'with email:', email);

      const response = await fetch('/api/trustees/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trusteeId,
          email
        }),
      });

      if (!response.ok) {
        console.error('Error creating invitation session:', response.status);
        return;
      }

      const data = await response.json();

      if (data.success && data.session_token) {
        // Store the session token in a cookie instead of localStorage
        document.cookie = `trustee_session=${data.session_token}; path=/; max-age=86400; SameSite=Lax`;
        console.log('Created and stored new trustee session token in cookie');
      }
    } catch (error) {
      console.error('Exception creating invitation session:', error);
    }
  };

  const fetchInvitation = async (inviteId: string) => {
    try {
      setIsLoading(true);
      console.log('Fetching invitation with ID:', inviteId);

      // Use the API endpoint to fetch the invitation
      const url = `/api/trustees/invitation?id=${inviteId}`;

      console.log('Fetching from URL:', url);

      // If we get a 406 error from the API, try to fetch directly from Supabase
      let invitation = null;
      let inviter = null;

      try {
        const response = await fetch(url);

        if (!response.ok) {
          let errorMessage = 'Failed to fetch invitation';
          try {
            // Try to parse the error response as JSON
            const errorData = await response.json();
            errorMessage = errorData.error || errorMessage;
          } catch (parseError) {
            // If parsing fails, try to get the text response
            try {
              const errorText = await response.text();
              if (errorText) errorMessage = errorText;
            } catch (textError) {
              // If that also fails, use the status code
              errorMessage = `Failed to fetch invitation (${response.status})`;
            }
          }
          console.error('Error response from API:', response.status, errorMessage);

          // If it's a 406 error, we'll try to fetch directly from Supabase
          if (response.status === 406) {
            console.log('Got 406 error, trying direct Supabase fetch');
            throw new Error('406 error');
          } else {
            throw new Error(errorMessage);
          }
        }

        // If we get here, the API call was successful
        const data = await response.json();
        invitation = data.invitation;
        inviter = data.inviter;
      } catch (apiError) {
        // Try to fetch directly from Supabase as a fallback
        console.log('Trying direct Supabase fetch for invitation');

        try {
          // Get the invitation details
          const { data: invitationData, error: inviteError } = await supabase
            .from('trustees')
            .select('*')
            .eq('id', inviteId)
            .single();

          if (inviteError || !invitationData) {
            console.error('Error fetching invitation from Supabase:', inviteError);
            throw new Error('Invitation not found');
          }

          invitation = invitationData;

          // Create a minimal inviter object
          inviter = {
            id: invitation.user_id,
            first_name: 'Legalock',
            last_name: 'User',
            email: '<EMAIL>'
          };

          console.log('Successfully fetched invitation directly from Supabase');
        } catch (supabaseError) {
          console.error('Error fetching from Supabase:', supabaseError);
          throw new Error('Failed to fetch invitation details');
        }
      }

      // At this point, we have either the invitation and inviter from the API
      // or directly from Supabase
      console.log('Invitation data received:', {
        invitationId: invitation?.id,
        inviterName: inviter ? `${inviter.first_name} ${inviter.last_name}` : 'Unknown',
        trusteeEmail: invitation?.trustee_email,
        status: invitation?.status,
        currentUser: user?.id,
        userLoggedIn: !!user
      });

      // We don't have currentUserId when fetching directly from Supabase
      const currentUserId = user?.id;

      // If the user is logged in but the UI shows they're not, reload the page
      // This can happen due to cookie/session issues
      if (user && currentUserId && user.id !== currentUserId) {
        console.warn('User ID mismatch between client and server. Reloading page.');
        window.location.reload();
        return;
      }

      if (!invitation) {
        setError('Invitation not found');
        return;
      }

      if (invitation.status === 'revoked') {
        setError('This invitation has been revoked');
        return;
      }

      if (invitation.status === 'active') {
        setError('This invitation has already been accepted');
        return;
      }

      // Set the invitation and inviter data
      setInvitation(invitation);
      setInviter(inviter);

      console.log('Successfully loaded invitation and inviter data');
      console.log('Invitation status:', invitation.status);
      console.log('Inviter:', `${inviter.first_name} ${inviter.last_name}`);
      console.log('Trustee email:', invitation.trustee_email);
    } catch (error: any) {
      console.error('Error fetching invitation:', error);
      setError('Failed to load invitation details');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to link a trustee to a user account using the unified API endpoint
  const linkTrusteeToUser = async (trusteeId: string, userId: string, email?: string, token?: string) => {
    try {
      console.log('Activating trustee using API endpoint:', { trusteeId, userId, email });

      // Use the unified API endpoint to activate the trustee
      const response = await fetch('/api/trustees/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trusteeId,
          email,
          token
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Error activating trustee:', data.error);
        toast.error(data.error || 'Failed to activate trustee. Please try again.');
        return false;
      }

      if (data.nameUpdated) {
        console.log(`Name updated from "${data.originalName}" to "${data.updatedName}"`);
      }

      console.log('Successfully activated trustee:', data);
      toast.success('You are now a trustee!');
      return true;
    } catch (error: any) {
      console.error('Error activating trustee:', error);
      toast.error(error.message || 'Failed to activate trustee');
      return false;
    }
  };

  const handleAcceptInvitation = async () => {
    try {
      if (!acceptTerms) {
        toast.error('Please accept the trustee responsibilities before continuing');
        return;
      }

      setIsAccepting(true);

      // Get the session token from the cookie
      const sessionToken = getCookie('trustee_session');

      if (sessionToken) {
        // Update the session to mark terms as accepted
        await updateInvitationSession(sessionToken, true);
        console.log('Updated invitation session to mark terms as accepted');
      }

      // If the user is logged in, activate the trustee immediately
      if (user) {
        console.log('User is logged in, activating trustee for account:', user.id);

        // Check if the user's email matches the invitation email
        if (user.email && invitation.trustee_email &&
            user.email.toLowerCase() !== invitation.trustee_email.toLowerCase()) {
          console.warn(`Email mismatch: Invitation for ${invitation.trustee_email}, but user logged in with ${user.email}`);
          toast.warning(`This invitation was sent to ${invitation.trustee_email}, but you're logged in with ${user.email}. Please log in with the correct email or contact the person who invited you.`);
          // Clear the session cookie
          document.cookie = 'trustee_session=; path=/; max-age=0; SameSite=Lax';
          setIsAccepting(false);
          return;
        }

        // Activate the trustee using our unified function
        const activated = await linkTrusteeToUser(
          invitation.id,
          user.id,
          invitation.trustee_email,
          sessionToken || undefined
        );

        if (!activated) return;

        // Mark the session as used if we have one
        if (sessionToken) {
          await updateInvitationSession(sessionToken, null, true);
          console.log('Marked invitation session as used');
        }

        // Clear the session cookie
        document.cookie = 'trustee_session=; path=/; max-age=0; SameSite=Lax';

        // Redirect to dashboard after successful activation
        toast.success('Invitation accepted successfully');
        router.push('/dashboard');
        return;
      } else {
        // If the user is not logged in, redirect to authentication
        console.log('User not logged in, redirecting to authentication');

        // Get the session token from the cookie
        const sessionToken = getCookie('trustee_session');
        let userExists = false;

        // If we have a session token, validate it to check if the user exists
        if (sessionToken) {
          try {
            // Call the validate-session API to check if the user exists
            const response = await fetch('/api/trustees/validate-session', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                sessionToken
              }),
            });

            if (response.ok) {
              const data = await response.json();
              if (data.valid) {
                // Use the user_exists field from the session validation
                userExists = data.user_exists || false;
                console.log('Session validation result - user exists:', userExists);
              }
            }
          } catch (error) {
            console.error('Error validating session to check user existence:', error);
          }
        }

        // If we couldn't determine from the session, fall back to checking the database
        if (!sessionToken || userExists === undefined) {
          // Check if the email matches an existing account using client-side methods
          // First check custom_users and profiles tables
          const { data: existingCustomUser } = await supabase
            .from('custom_users')
            .select('id, email')
            .eq('email', invitation.trustee_email.toLowerCase())
            .maybeSingle();

          const { data: existingProfile } = await supabase
            .from('profiles')
            .select('id, email')
            .eq('email', invitation.trustee_email.toLowerCase())
            .maybeSingle();

          userExists = !!(existingCustomUser || existingProfile);
        }

        // Get the session token to pass along to the login/register page
        const sessionParam = sessionToken ? `&session=${sessionToken}` : '';

        if (userExists) {
          console.log('Existing user found, redirecting to login');
          toast.success('Please sign in to complete your trustee invitation');
          router.push(`/login?redirect=/dashboard&trusteeId=${invitation.id}${sessionParam}`);
        } else {
          console.log('No existing user found, redirecting to register');
          toast.success('Please create an account to complete your trustee invitation');

          // Make sure we have the first and last name from the invitation
          const firstName = invitation.first_name || '';
          const lastName = invitation.last_name || '';

          console.log(`Redirecting to register with name: ${firstName} ${lastName}`);

          router.push(`/register?redirect=/dashboard&trusteeId=${invitation.id}&email=${encodeURIComponent(invitation.trustee_email)}&trustee=${invitation.id}&firstName=${encodeURIComponent(firstName)}&lastName=${encodeURIComponent(lastName)}${sessionParam}`);
        }
        return;
      }
    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      toast.error(error.message || 'Failed to accept invitation');
    } finally {
      setIsAccepting(false);
    }
  };

  // Helper function to get a cookie value by name
  const getCookie = (name: string): string | null => {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(name + '=')) {
        return cookie.substring(name.length + 1);
      }
    }
    return null;
  };

  // Function to update an invitation session
  const updateInvitationSession = async (
    sessionToken: string,
    termsAccepted: boolean | null = null,
    used: boolean | null = null
  ) => {
    try {
      const response = await fetch('/api/trustees/update-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionToken,
          termsAccepted,
          used
        }),
      });

      if (!response.ok) {
        console.error('Error updating invitation session:', response.status);
        return false;
      }

      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('Exception updating invitation session:', error);
      return false;
    }
  };

  const handleDeclineInvitation = async () => {
    try {
      setIsDeclining(true);

      // Update the trustee record
      const { error } = await supabase
        .from('trustees')
        .update({
          status: 'revoked',
        })
        .eq('id', invitation.id);

      if (error) throw error;

      // Mark the session as used if we have one
      const sessionToken = getCookie('trustee_session');
      if (sessionToken) {
        await updateInvitationSession(sessionToken, null, true);
        console.log('Marked invitation session as used due to decline');

        // Clear the session cookie
        document.cookie = 'trustee_session=; path=/; max-age=0; SameSite=Lax';
      }

      toast.success('Invitation declined');
      router.push('/');
    } catch (error: any) {
      console.error('Error declining invitation:', error);
      toast.error(error.message || 'Failed to decline invitation');
    } finally {
      setIsDeclining(false);
    }
  };

  const renderPermissions = (permissions: string[]) => {
    const permissionLabels: Record<string, string> = {
      assets: 'Digital and physical assets',
      vault: 'Digital vault documents',
      contacts: 'Emergency contacts',
      services: 'Services to cancel',
    };

    return (
      <ul className="list-disc pl-5 space-y-1">
        {permissions.map((permission) => (
          <li key={permission}>
            {permissionLabels[permission] || permission}
          </li>
        ))}
      </ul>
    );
  };

  // Always use AuthHeader for the accept page to ensure consistent UI
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <AuthHeader />
      <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md">
          {isLoading ? (
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-500">Loading invitation...</p>
            </div>
          ) : error ? (
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Invitation Error</CardTitle>
                <CardDescription>
                  {error}
                </CardDescription>
              </CardHeader>
              <CardFooter className="flex justify-center">
                <Button asChild>
                  <Link href="/">
                    Return to Home
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ) : invitation && inviter ? (
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Trustee Invitation</CardTitle>
                <CardDescription>
                  You've been invited to be a trustee
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center mb-4">
                  <p className="text-lg font-medium">
                    {inviter.first_name} {inviter.last_name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {inviter.email}
                  </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <p className="text-sm text-blue-800">
                      <strong>{inviter.first_name}</strong> has invited you to be their trustee. As a trustee, you will be responsible for managing their digital legacy after their passing.
                    </p>
                  </div>
                  <div className="text-sm text-blue-800 mt-2">
                    <p>Being a trustee is an important responsibility. You will only gain access to the designated information after verification of {inviter.first_name}'s passing.</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">You will have access to:</p>
                  {renderPermissions(invitation.permissions)}
                </div>

                <div className="pt-4 space-y-4">
                  <div className="bg-amber-50 p-4 rounded-md">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-600 mr-2 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-amber-800">Important Information</h4>
                        <p className="text-sm text-amber-700 mt-1">
                          As a trustee, you will be responsible for managing aspects of {inviter.first_name}'s digital legacy. This may include closing accounts, transferring assets, and ensuring their final wishes are respected.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={acceptTerms}
                      onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
                    />
                    <label
                      htmlFor="terms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      I understand and accept my responsibilities as a trustee for {inviter.first_name} {inviter.last_name}
                    </label>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-3">
                <Button
                  className="w-full"
                  onClick={handleAcceptInvitation}
                  disabled={isAccepting || !acceptTerms}
                >
                  {isAccepting ? (
                    <>
                      <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Accept Invitation
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleDeclineInvitation}
                  disabled={isDeclining}
                >
                  {isDeclining ? 'Declining...' : 'Decline'}
                </Button>
              </CardFooter>
            </Card>
          ) : null}
        </div>
      </div>
    </div>
  );
}

// Export the page component with Suspense
export default function AcceptTrusteeInvitationPage() {
  return (
    <Suspense fallback={<ClientFallback />}>
      <TrusteeInvitationContent />
    </Suspense>
  );
}
