import React from 'react';
import Link from 'next/link';
import { FileText, Shield, CheckCircle, ArrowLeft, ArrowRight, Users, Settings, Clock, UserCheck, AlertCircle, RefreshCw, Mail, BookOpen, CreditCard, Upload, UserPlus, Briefcase, Scale, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export const metadata = {
  title: 'Terms of Service | Legalock',
  description: 'Read the terms and conditions governing the use of Legalock services.',
};

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 py-6">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-indigo-600 to-indigo-800 py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-flex items-center justify-center p-3 rounded-full bg-white/20 mb-6">
              <FileText className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">Terms of Service</h1>
            <p className="text-xl text-white/90 mb-2">Last Updated: June 1, 2023</p>
            <p className="text-lg text-white/80">
              Please read these terms carefully before using our services.
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <Card className="mb-12 shadow-md">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                    <Shield className="h-6 w-6 text-indigo-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Important Notice</h2>
                    <p className="text-gray-600">By using Legalock, you agree to these terms and conditions in full.</p>
                  </div>
                </div>
                <p className="text-gray-700">
                  These Terms of Service constitute a legally binding agreement made between you and Legalock ("we," "us," or "our"),
                  concerning your access to and use of the Legalock website and services. If you do not agree with all of these Terms of Service,
                  then you are expressly prohibited from using the Site and/or Services and you must discontinue use immediately.
                </p>
              </CardContent>
            </Card>

            <div className="prose prose-headings:text-gray-900 prose-headings:font-bold prose-h2:text-2xl prose-h3:text-xl prose-p:text-gray-700 prose-li:text-gray-700 prose-ul:my-6 prose-ul:space-y-2 max-w-none bg-white p-8 rounded-xl shadow-md">
              <h2 className="flex items-center pb-2 border-b border-gray-200">
                <BookOpen className="h-5 w-5 text-indigo-600 mr-2" />
                1. Agreement to Terms
              </h2>
              <p>
                These Terms of Service constitute a legally binding agreement made between you and Legalock ("we," "us," or "our"), concerning your access to and use of the Legalock website and services.
              </p>
              <p>
                You agree that by accessing the Site and/or Services, you have read, understood, and agree to be bound by all of these Terms of Service. If you do not agree with all of these Terms of Service, then you are expressly prohibited from using the Site and/or Services and you must discontinue use immediately.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <Settings className="h-5 w-5 text-indigo-600 mr-2" />
                2. Description of Services
              </h2>
              <p>
                Legalock provides a digital legacy planning platform that allows users to:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li className="pl-2">Document digital and physical assets</li>
                <li className="pl-2">Store important documents in an encrypted vault</li>
                <li className="pl-2">Designate trustees to manage their digital legacy</li>
                <li className="pl-2">Create a list of emergency contacts</li>
                <li className="pl-2">Document services to be canceled after passing</li>
                <li className="pl-2">Record final wishes and preferences</li>
              </ul>
              <p>
                Our services are designed to help users organize their digital legacy and ensure their wishes are carried out after their passing.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <UserCheck className="h-5 w-5 text-indigo-600 mr-2" />
                3. User Accounts
              </h2>
              <p>
                When you create an account with us, you must provide information that is accurate, complete, and current at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our Service.
              </p>
              <p>
                You are responsible for safeguarding the password that you use to access the Service and for any activities or actions under your password. You agree not to disclose your password to any third party. You must notify us immediately upon becoming aware of any breach of security or unauthorized use of your account.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <CreditCard className="h-5 w-5 text-indigo-600 mr-2" />
                4. Subscription and Payments
              </h2>
              <p>
                Legalock offers both free and premium subscription plans. By selecting a premium plan, you agree to pay the subscription fees indicated for that service. Payments will be charged on the day you sign up for a premium plan and will cover the use of that service for the subscription period indicated.
              </p>
              <p>
                Subscription fees are non-refundable except as required by law or as explicitly stated in these Terms. We reserve the right to change subscription fees upon reasonable notice.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <Upload className="h-5 w-5 text-indigo-600 mr-2" />
                5. User Content
              </h2>
              <p>
                Our Service allows you to store, upload, submit, and share content, including documents, text, and other materials ("User Content"). You retain all rights in, and are solely responsible for, the User Content you upload, post, or share.
              </p>
              <p>
                By uploading User Content, you grant us a non-exclusive, royalty-free license to use, store, and process your User Content solely for the purpose of providing and improving the Service.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <UserPlus className="h-5 w-5 text-indigo-600 mr-2" />
                6. Trustee System
              </h2>
              <p>
                The Legalock trustee system allows you to designate individuals who will have access to your digital legacy information after your passing. By designating a trustee, you acknowledge and agree that:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li className="pl-2">You are granting permission for the trustee to access your account information after your passing is verified</li>
                <li className="pl-2">You are responsible for ensuring your trustees understand their responsibilities</li>
                <li className="pl-2">Legalock is not responsible for the actions taken by your trustees</li>
                <li className="pl-2">You can revoke trustee access at any time while you are alive</li>
              </ul>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <Briefcase className="h-5 w-5 text-indigo-600 mr-2" />
                7. Intellectual Property
              </h2>
              <p>
                The Site and its original content (excluding User Content), features, and functionality are and will remain the exclusive property of Legalock and its licensors. The Site is protected by copyright, trademark, and other laws of both the United States and foreign countries. Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Legalock.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <XCircle className="h-5 w-5 text-indigo-600 mr-2" />
                8. Termination
              </h2>
              <p>
                We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.
              </p>
              <p>
                Upon termination, your right to use the Service will immediately cease. If you wish to terminate your account, you may simply discontinue using the Service or contact us to request account deletion.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <AlertCircle className="h-5 w-5 text-indigo-600 mr-2" />
                9. Limitation of Liability
              </h2>
              <p>
                In no event shall Legalock, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li className="pl-2">Your access to or use of or inability to access or use the Service</li>
                <li className="pl-2">Any conduct or content of any third party on the Service</li>
                <li className="pl-2">Any content obtained from the Service</li>
                <li className="pl-2">Unauthorized access, use, or alteration of your transmissions or content</li>
              </ul>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <Shield className="h-5 w-5 text-indigo-600 mr-2" />
                10. Disclaimer
              </h2>
              <p>
                Your use of the Service is at your sole risk. The Service is provided on an "AS IS" and "AS AVAILABLE" basis. The Service is provided without warranties of any kind, whether express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.
              </p>
              <p>
                Legalock does not warrant that: (a) the Service will function uninterrupted, secure, or available at any particular time or location; (b) any errors or defects will be corrected; (c) the Service is free of viruses or other harmful components; or (d) the results of using the Service will meet your requirements.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <Scale className="h-5 w-5 text-indigo-600 mr-2" />
                11. Governing Law
              </h2>
              <p>
                These Terms shall be governed and construed in accordance with the laws of the United States, without regard to its conflict of law provisions.
              </p>
              <p>
                Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions of these Terms will remain in effect.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <RefreshCw className="h-5 w-5 text-indigo-600 mr-2" />
                12. Changes to Terms
              </h2>
              <p>
                We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.
              </p>
              <p>
                By continuing to access or use our Service after those revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, please stop using the Service.
              </p>

              <h2 className="flex items-center pb-2 border-b border-gray-200 mt-8">
                <Mail className="h-5 w-5 text-indigo-600 mr-2" />
                13. Contact Us
              </h2>
              <p>
                If you have any questions about these Terms, please contact us at:
              </p>
              <p>
                <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
              </p>
            </div>

            {/* CTA Section */}
            <div className="mt-12 text-center bg-gray-100 p-8 rounded-xl border border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to secure your digital legacy?</h2>
              <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
                Join thousands of users who trust Legalock to protect their digital assets and final wishes.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button size="lg" asChild>
                  <Link href="/register">
                    Get Started for Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-gray-500 text-sm">
            <p>© {new Date().getFullYear()} Legalock. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
