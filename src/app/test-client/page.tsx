"use client";

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase-unified';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export default function TestClientPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [supabaseStatus, setSupabaseStatus] = useState<any>(null);
  const [apiStatus, setApiStatus] = useState<any>(null);

  useEffect(() => {
    async function checkSupabase() {
      try {
        setLoading(true);
        
        // Check if Supabase client is initialized
        const clientStatus = {
          initialized: !!supabase,
          hasAuthMethods: supabase && typeof supabase.auth === 'object',
        };
        
        setSupabaseStatus(clientStatus);
        
        // Check API status
        const response = await fetch('/api/test-supabase');
        const data = await response.json();
        setApiStatus(data);
      } catch (err: any) {
        console.error('Error checking Supabase:', err);
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    }
    
    checkSupabase();
  }, []);

  return (
    <div className="container mx-auto py-16 px-4">
      <h1 className="text-3xl font-bold mb-8">Supabase Client Test</h1>
      
      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
          <span>Testing Supabase connection...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-red-600">{error}</p>
        </div>
      ) : (
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Client-Side Supabase Status</CardTitle>
              <CardDescription>Status of the Supabase client in the browser</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                {JSON.stringify(supabaseStatus, null, 2)}
              </pre>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>API Supabase Status</CardTitle>
              <CardDescription>Status of the Supabase client from the API</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto">
                {JSON.stringify(apiStatus, null, 2)}
              </pre>
            </CardContent>
          </Card>
          
          <CardFooter className="flex justify-center">
            <Button onClick={() => window.location.reload()}>
              Refresh Test
            </Button>
          </CardFooter>
        </div>
      )}
    </div>
  );
}
