import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Sunset, Shield, CheckCircle, Clock, CreditCard, ArrowRight } from 'lucide-react';
import FeaturePageLayout from '@/components/Layout/FeaturePageLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const metadata = {
  title: 'Service Sunset | Legalock',
  description: 'List services and subscriptions that should be canceled after your passing with detailed cancellation instructions.',
};

export default function ServiceSunsetPage() {
  return (
    <FeaturePageLayout
      title="Service Sunset"
      description="List services and subscriptions that should be canceled after your passing with detailed cancellation instructions."
      iconName="Sunset"
      iconColor="text-orange-100"
      iconBgColor="bg-gradient-to-r from-orange-600 to-orange-800"
    >
      <div className="max-w-4xl mx-auto">
        {/* Overview Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Why Use Service Sunset?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <p className="text-lg text-gray-700 mb-4">
                In today's subscription economy, most of us have numerous recurring payments for digital services, memberships, and subscriptions. When someone passes away, these services continue to bill until manually canceled, potentially draining estate resources.
              </p>
              <p className="text-lg text-gray-700 mb-4">
                Legalock's Service Sunset feature helps you create a comprehensive list of services that should be canceled after your passing, complete with account details and cancellation instructions for your trustees.
              </p>
            </div>
            <div className="bg-gray-100 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Did You Know?</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">The average American has 12 active subscriptions</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Uncanceled subscriptions cost estates an average of $348 per year</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Many services have no automatic process for cancellation after death</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Some services require specific documentation to cancel accounts of deceased users</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                  <CreditCard className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle>Document Services</CardTitle>
                <CardDescription>Create a comprehensive list</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Document all your subscriptions, memberships, and recurring services, including account details and cancellation methods.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle>Prioritize Cancellations</CardTitle>
                <CardDescription>Set importance levels</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Mark services as high, medium, or low priority to help your trustees know which ones to cancel first to prevent unnecessary charges.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle>Trustee Access</CardTitle>
                <CardDescription>Available when needed</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  After your passing is verified, your trustees gain access to your service list and can methodically cancel each one according to your instructions.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Trustee Role Section */}
        <div className="mb-16 bg-gray-50 p-8 rounded-xl border border-gray-200">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">The Trustee's Role</h2>
          <div className="space-y-6">
            <p className="text-lg text-gray-700">
              When you designate someone as a trustee for your Service Sunset, you're entrusting them with canceling your subscriptions and services. Here's what your trustees will do:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Before Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Receive notification of their designation as a trustee</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Accept the trustee role and responsibilities</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Have no access to your service information</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">After Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Report your passing through the Legalock platform</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Gain access to your service list after verification</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Cancel services according to priority level</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Follow your detailed cancellation instructions</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Mark services as canceled in the platform</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Request refunds for prepaid services when applicable</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Service Categories</h3>
                <p className="text-gray-700">
                  Organize services by category: streaming, utilities, memberships, subscriptions, and more for easy management.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Priority Levels</h3>
                <p className="text-gray-700">
                  Assign high, medium, or low priority to each service, helping trustees focus on canceling the most important or expensive ones first.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Detailed Cancellation Instructions</h3>
                <p className="text-gray-700">
                  Provide step-by-step instructions for canceling each service, including phone numbers, websites, and any special requirements.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Auto-Renewal Tracking</h3>
                <p className="text-gray-700">
                  Flag services with auto-renewal and track renewal dates to help trustees cancel before the next billing cycle.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Cost Tracking</h3>
                <p className="text-gray-700">
                  Record the cost of each service and billing frequency to help trustees understand the financial impact of each subscription.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Completion Tracking</h3>
                <p className="text-gray-700">
                  Trustees can mark services as canceled, creating a clear record of which subscriptions have been addressed.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Common Services Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Common Services to Include</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Digital Subscriptions</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Streaming Services (Netflix, Hulu, etc.)</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Music Services (Spotify, Apple Music)</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Cloud Storage (Dropbox, Google Drive)</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Software Subscriptions (Adobe, Microsoft)</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Gaming Services (Xbox Game Pass, PlayStation Plus)</span>
                </li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Utilities & Services</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Mobile Phone Plans</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Internet Service</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Cable TV</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Electricity & Gas</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Water & Sewage</span>
                </li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Memberships & Other</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Gym Memberships</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Club Memberships</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Subscription Boxes</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Magazine & Newspaper Subscriptions</span>
                </li>
                <li className="flex items-center">
                  <Sunset className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-gray-700">Charitable Recurring Donations</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Start Documenting Your Services Today</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            Don't leave your loved ones dealing with unwanted subscriptions and recurring charges. Create your Service Sunset plan now and ensure a smooth transition.
          </p>
          <Button size="lg" asChild>
            <Link href="/register">
              Get Started for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </FeaturePageLayout>
  );
}
