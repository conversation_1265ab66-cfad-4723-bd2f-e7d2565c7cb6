import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Heart, Shield, CheckCircle, Leaf, PawPrint, ArrowRight } from 'lucide-react';
import FeaturePageLayout from '@/components/Layout/FeaturePageLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const metadata = {
  title: 'Last Wishes | Legalock',
  description: 'Document your final wishes, including funeral preferences, organ donation status, and pet care instructions.',
};

export default function LastWishesPage() {
  return (
    <FeaturePageLayout
      title="Last Wishes"
      description="Document your final wishes, including funeral preferences, organ donation status, and pet care instructions."
      iconName="Heart"
      iconColor="text-red-100"
      iconBgColor="bg-gradient-to-r from-red-600 to-red-800"
    >
      <div className="max-w-4xl mx-auto">
        {/* Overview Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Why Document Your Last Wishes?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <p className="text-lg text-gray-700 mb-4">
                Your final wishes are deeply personal expressions of how you want to be remembered and how you'd like certain matters handled after your passing. Without clear documentation, your loved ones may struggle to make decisions during an already difficult time.
              </p>
              <p className="text-lg text-gray-700 mb-4">
                Legalock's Last Wishes feature provides a structured way to document these important preferences, ensuring your voice is heard even when you're no longer able to speak for yourself.
              </p>
            </div>
            <div className="bg-gray-100 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Did You Know?</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Only 34% of Americans have documented their funeral preferences</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">54% of people with pets have not made arrangements for their care</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Families who know their loved one's wishes report less stress and conflict during bereavement</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                  <Heart className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Document Your Wishes</CardTitle>
                <CardDescription>Create a comprehensive record</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Document your preferences for funeral services, burial arrangements, personal messages to loved ones, and other final wishes in one secure location.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                  <Leaf className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Special Considerations</CardTitle>
                <CardDescription>Organ donation and pet care</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Indicate your organ donation status and registered state. If you have pets, provide detailed care instructions for their well-being after your passing.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Trustee Access</CardTitle>
                <CardDescription>Available when needed</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Your last wishes remain private during your lifetime. After your passing is verified, your trustees gain access to ensure your wishes are honored.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Trustee Role Section */}
        <div className="mb-16 bg-gray-50 p-8 rounded-xl border border-gray-200">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">The Trustee's Role</h2>
          <div className="space-y-6">
            <p className="text-lg text-gray-700">
              When you designate someone as a trustee for your Last Wishes, you're entrusting them with ensuring your final preferences are honored. Here's what your trustees will do:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Before Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Receive notification of their designation as a trustee</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Accept the trustee role and responsibilities</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Have no access to your last wishes</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">After Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Report your passing through the Legalock platform</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Gain access to your documented last wishes after verification</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Coordinate funeral and memorial arrangements according to your preferences</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Ensure your organ donation wishes are respected</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Arrange for the care of your pets as specified</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Share your personal messages with designated recipients</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">What You Can Document</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Funeral & Memorial Preferences</h3>
                <p className="text-gray-700">
                  Document your preferences for funeral services, memorial gatherings, or celebrations of life, including location, music, readings, and other personal touches.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Burial & Remains Preferences</h3>
                <p className="text-gray-700">
                  Specify your wishes for burial, cremation, or other arrangements for your remains, including preferred location and any special requests.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                <Leaf className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Organ Donation Status</h3>
                <p className="text-gray-700">
                  Indicate whether you're registered as an organ donor and specify the state where you're registered, ensuring your wishes are known and honored.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                <PawPrint className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Pet Care Instructions</h3>
                <p className="text-gray-700">
                  Provide detailed instructions for the care of your pets, including their needs, preferences, medical information, and your wishes for their future homes.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Personal Messages</h3>
                <p className="text-gray-700">
                  Leave personal messages, thoughts, or reflections for your loved ones, providing comfort and closure during their time of grief.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Other Final Wishes</h3>
                <p className="text-gray-700">
                  Document any other final wishes or instructions not covered in the other sections, ensuring all your preferences are known and respected.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonial Section */}
        <div className="mb-16 bg-red-50 p-8 rounded-xl border border-red-100">
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="w-24 h-24 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
              <Heart className="h-10 w-10 text-red-600" />
            </div>
            <div>
              <p className="text-lg text-gray-700 italic mb-4">
                "Documenting my last wishes with Legalock gave me peace of mind knowing my family won't have to guess what I would have wanted. The pet care section was especially important to me—knowing my dogs will be cared for exactly as I specified is a huge relief."
              </p>
              <p className="font-medium text-gray-900">Sarah M., Legalock User</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Document Your Last Wishes Today</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            Don't leave your loved ones guessing about your final wishes. Create a clear record of your preferences now and provide guidance when it matters most.
          </p>
          <Button size="lg" asChild>
            <Link href="/register">
              Get Started for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </FeaturePageLayout>
  );
}
