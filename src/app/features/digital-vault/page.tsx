import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { HardDrive, Shield, CheckCircle, Lock, FileText, ArrowRight } from 'lucide-react';
import FeaturePageLayout from '@/components/Layout/FeaturePageLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const metadata = {
  title: 'Digital Vault | Legalock',
  description: 'Store important documents in an encrypted vault accessible only to designated trustees after your passing.',
};

export default function DigitalVaultPage() {
  return (
    <FeaturePageLayout
      title="Digital Vault"
      description="Store important documents in an encrypted vault accessible only to designated trustees after your passing."
      iconName="HardDrive"
      iconColor="text-indigo-100"
      iconBgColor="bg-gradient-to-r from-indigo-600 to-indigo-800"
    >
      <div className="max-w-4xl mx-auto">
        {/* Overview Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Secure Storage for Your Most Important Documents</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <p className="text-lg text-gray-700 mb-4">
                Your important documents—wills, insurance policies, property deeds, and personal letters—deserve the highest level of protection and accessibility when needed.
              </p>
              <p className="text-lg text-gray-700 mb-4">
                Legalock's Digital Vault uses advanced encryption to keep your documents secure during your lifetime while ensuring they're accessible to your trustees when the time comes.
              </p>
            </div>
            <div className="bg-gray-100 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Why Use a Digital Vault?</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Physical documents can be lost, damaged, or destroyed</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Trustees may not know where to find critical documents</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Encrypted storage protects sensitive information from unauthorized access</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Centralized access simplifies the estate management process</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                  <FileText className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Upload Documents</CardTitle>
                <CardDescription>Securely store important files</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Upload wills, insurance policies, property deeds, personal letters, and other important documents to your secure vault.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                  <Lock className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Automatic Encryption</CardTitle>
                <CardDescription>Military-grade protection</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  All documents are automatically encrypted using AES-256 encryption, ensuring they remain private and secure during your lifetime.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Trustee Access</CardTitle>
                <CardDescription>Available when needed</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  After your passing is verified, your designated trustees gain access to the documents you've specified for them.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Trustee Role Section */}
        <div className="mb-16 bg-gray-50 p-8 rounded-xl border border-gray-200">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">The Trustee's Role</h2>
          <div className="space-y-6">
            <p className="text-lg text-gray-700">
              When you designate someone as a trustee for your Digital Vault, you're entrusting them with access to your most important documents. Here's what your trustees will do:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Before Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Receive notification of their designation as a vault trustee</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Accept the trustee role and responsibilities</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Have no access to your vault documents</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">After Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Report your passing through the Legalock platform</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Gain access to your vault documents after verification</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Download and view the documents you've shared with them</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Use these documents to manage your estate according to your wishes</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Share documents with relevant parties as specified in your instructions</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">AES-256 Encryption</h3>
                <p className="text-gray-700">
                  Military-grade encryption ensures your documents remain private and secure until they need to be accessed.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Document Categories</h3>
                <p className="text-gray-700">
                  Organize documents by category for easy management: legal, financial, personal, medical, and more.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Granular Access Control</h3>
                <p className="text-gray-700">
                  Specify which trustees can access which documents, ensuring sensitive information is only shared with appropriate individuals.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Document Notes</h3>
                <p className="text-gray-700">
                  Add detailed notes to each document, providing context and instructions for your trustees.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Document Types Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Recommended Documents to Store</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Legal Documents</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Last Will and Testament</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Power of Attorney</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Trust Documents</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Property Deeds</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Marriage Certificates</span>
                </li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Financial Documents</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Insurance Policies</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Investment Statements</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Tax Returns</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Pension Information</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Loan Documents</span>
                </li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Personal Documents</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Birth Certificates</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Medical Records</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Personal Letters</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Family Photos</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-4 w-4 text-indigo-500 mr-2" />
                  <span className="text-gray-700">Funeral Instructions</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Secure Your Important Documents Today</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            Don't leave your loved ones searching for critical documents. Create your Digital Vault now and ensure everything is accessible when needed.
          </p>
          <Button size="lg" asChild>
            <Link href="/register">
              Get Started for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </FeaturePageLayout>
  );
}
