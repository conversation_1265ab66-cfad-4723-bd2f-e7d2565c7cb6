import React from 'react';
import Link from 'next/link';
import { UserPlus, Shield, CheckCircle, ArrowRight } from 'lucide-react';
import FeaturePageLayout from '@/components/Layout/FeaturePageLayout';
import { Button } from '@/components/ui/button';

export const metadata = {
  title: 'Emergency Contacts | Legalock',
  description: 'Create a list of people who should be notified by your trustees after your passing with their contact information.',
};

export default function EmergencyContactsPage() {
  return (
    <FeaturePageLayout
      title="Emergency Contacts"
      description="Create a list of people who should be notified by your trustees after your passing with their contact information."
      iconName="UserPlus"
      iconColor="text-green-100"
      iconBgColor="bg-gradient-to-r from-green-600 to-green-800"
    >
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Coming Soon</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            We're currently working on detailed information about our Emergency Contacts feature. 
            Check back soon for a comprehensive guide on how to create and manage your emergency contact list.
          </p>
          <Button size="lg" asChild>
            <Link href="/register">
              Get Started for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </FeaturePageLayout>
  );
}
