import React from 'react';
import Link from 'next/link';
import { Users, Shield, CheckCircle, ArrowRight } from 'lucide-react';
import FeaturePageLayout from '@/components/Layout/FeaturePageLayout';
import { Button } from '@/components/ui/button';

export const metadata = {
  title: 'Trustee Management | Legalock',
  description: 'Designate trusted individuals to manage your digital legacy and control what information each trustee can access.',
};

export default function TrusteeManagementPage() {
  return (
    <FeaturePageLayout
      title="Trustee Management"
      description="Designate trusted individuals to manage your digital legacy and control what information each trustee can access."
      iconName="Users"
      iconColor="text-purple-100"
      iconBgColor="bg-gradient-to-r from-purple-600 to-purple-800"
    >
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Coming Soon</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            We're currently working on detailed information about our Trustee Management feature. 
            Check back soon for a comprehensive guide on how to designate and manage trustees for your digital legacy.
          </p>
          <Button size="lg" asChild>
            <Link href="/register">
              Get Started for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </FeaturePageLayout>
  );
}
