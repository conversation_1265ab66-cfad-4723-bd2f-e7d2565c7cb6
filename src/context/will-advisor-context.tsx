"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  Answer,
  Question,
  Recommendation,
  WillAdvisorState
} from '@/types/will-advisor.types';
import { willAdvisorQuestions, willAdvisorRecommendations } from '@/data/will-advisor-data';

interface WillAdvisorContextType {
  state: WillAdvisorState;
  questions: Question[];
  currentQuestion: Question | null;
  visibleQuestions: Question[];
  setAnswer: (answer: Answer) => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  resetAdvisor: () => void;
  completeAdvisor: () => void;
  getRecommendations: () => Recommendation[];
  getAnswerByQuestionId: (questionId: string) => Answer | undefined;
}

const initialState: WillAdvisorState = {
  currentStep: 0,
  answers: [],
  recommendations: [],
  completed: false,
};

const WillAdvisorContext = createContext<WillAdvisorContextType | undefined>(undefined);

export const WillAdvisorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<WillAdvisorState>(initialState);
  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);

  // Initialize with the first question
  useEffect(() => {
    updateVisibleQuestions();
  }, [state.answers]);

  // Update which questions should be visible based on current answers
  const updateVisibleQuestions = () => {
    const visible = willAdvisorQuestions.filter(question => {
      // If the question has no conditional display, it's always visible
      if (!question.conditionalDisplay) return true;

      // Check if the condition is met
      const { dependsOn, showIfValue } = question.conditionalDisplay;
      const dependentAnswer = state.answers.find(a => a.questionId === dependsOn);

      // If there's no answer yet for the dependent question, don't show this question
      if (!dependentAnswer) return false;

      // Check if the answer matches the condition
      if (Array.isArray(showIfValue)) {
        return showIfValue.includes(String(dependentAnswer.value));
      } else {
        return String(dependentAnswer.value) === showIfValue;
      }
    });

    setVisibleQuestions(visible);
  };

  const currentQuestion = state.currentStep < visibleQuestions.length
    ? visibleQuestions[state.currentStep]
    : null;

  const setAnswer = (answer: Answer) => {
    setState(prevState => {
      // Check if we already have an answer for this question
      const existingAnswerIndex = prevState.answers.findIndex(
        a => a.questionId === answer.questionId
      );

      let newAnswers;
      if (existingAnswerIndex >= 0) {
        // Update existing answer
        newAnswers = [...prevState.answers];
        newAnswers[existingAnswerIndex] = answer;
      } else {
        // Add new answer
        newAnswers = [...prevState.answers, answer];
      }

      return {
        ...prevState,
        answers: newAnswers,
      };
    });
  };

  const nextQuestion = () => {
    if (state.currentStep < visibleQuestions.length - 1) {
      setState(prevState => ({
        ...prevState,
        currentStep: prevState.currentStep + 1,
      }));
    } else {
      // If we're at the last question, complete the advisor
      completeAdvisor();
    }
  };

  const previousQuestion = () => {
    if (state.currentStep > 0) {
      setState(prevState => ({
        ...prevState,
        currentStep: prevState.currentStep - 1,
      }));
    }
  };

  const resetAdvisor = () => {
    setState(initialState);
  };

  const completeAdvisor = () => {
    const recommendations = getRecommendations();
    setState(prevState => ({
      ...prevState,
      recommendations,
      completed: true,
    }));
  };

  const getRecommendations = (): Recommendation[] => {
    return willAdvisorRecommendations.filter(recommendation => {
      if (!recommendation.conditionalDisplay) return true;

      const { logic, conditions } = recommendation.conditionalDisplay;

      const conditionResults = conditions.map(condition => {
        const { questionId, operator, value } = condition;
        const answer = state.answers.find(a => a.questionId === questionId);

        if (!answer) return false;

        switch (operator) {
          case 'equals':
            return answer.value === value;
          case 'notEquals':
            return answer.value !== value;
          case 'contains':
            if (Array.isArray(answer.value)) {
              if (Array.isArray(value)) {
                return value.some(v => (answer.value as string[]).includes(String(v)));
              } else {
                return (answer.value as string[]).includes(String(value));
              }
            }
            return false;
          case 'greaterThan':
            return typeof answer.value === 'number' &&
                   typeof value === 'number' &&
                   answer.value > value;
          case 'lessThan':
            return typeof answer.value === 'number' &&
                   typeof value === 'number' &&
                   answer.value < value;
          default:
            return false;
        }
      });

      if (logic === 'AND') {
        return conditionResults.every(result => result);
      } else {
        return conditionResults.some(result => result);
      }
    });
  };

  const getAnswerByQuestionId = (questionId: string): Answer | undefined => {
    return state.answers.find(answer => answer.questionId === questionId);
  };

  return (
    <WillAdvisorContext.Provider
      value={{
        state,
        questions: willAdvisorQuestions,
        currentQuestion,
        visibleQuestions,
        setAnswer,
        nextQuestion,
        previousQuestion,
        resetAdvisor,
        completeAdvisor,
        getRecommendations,
        getAnswerByQuestionId,
      }}
    >
      {children}
    </WillAdvisorContext.Provider>
  );
};

export const useWillAdvisor = () => {
  const context = useContext(WillAdvisorContext);
  if (context === undefined) {
    throw new Error('useWillAdvisor must be used within a WillAdvisorProvider');
  }
  return context;
};
