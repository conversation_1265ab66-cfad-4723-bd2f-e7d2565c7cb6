"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

/**
 * This component checks for pending trustee invitations when a user logs in or signs up.
 * It should be included in the layout or on pages where users land after authentication.
 */
export default function TrusteeInvitationChecker() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    // Only run this check when a user is authenticated and not already checking
    if (loading || !user || isChecking) return;

    // Helper function to get a cookie value by name
    const getCookie = (name: string): string | null => {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith(name + '=')) {
          return cookie.substring(name.length + 1);
        }
      }
      return null;
    };

    const checkForPendingInvitations = async () => {
      try {
        setIsChecking(true);
        console.log('Checking for pending trustee invitations for user:', user.id, 'with email:', user.email);

        // STEP 1: Check for session token in cookies or URL, or fallback to localStorage
        // Check for session token in cookies or URL
        const sessionToken = getCookie('trustee_session');
        const urlParams = new URLSearchParams(window.location.search);
        const urlSessionToken = urlParams.get('session');
        const urlTrusteeId = urlParams.get('trusteeId');

        // For backward compatibility, also check localStorage
        const pendingTrusteeId = localStorage.getItem('pendingTrusteeId');
        const pendingTrusteeEmail = localStorage.getItem('pendingTrusteeEmail');
        const termsAccepted = localStorage.getItem('trusteeTermsAccepted') === 'true';

        // Determine which trustee ID to use
        const trusteeId = urlTrusteeId || pendingTrusteeId;

        // Log all the data we've collected
        console.log('Collected trustee data:', {
          pendingTrusteeId,
          pendingTrusteeEmail,
          termsAccepted,
          urlTrusteeId,
          finalTrusteeId: trusteeId,
          sessionToken: sessionToken ? `${sessionToken.substring(0, 8)}...` : null,
          urlSessionToken: urlSessionToken ? `${urlSessionToken.substring(0, 8)}...` : null,
          currentPath: window.location.pathname
        });

        // If we're already on the accept page, don't do anything
        if (window.location.pathname.includes('/trustee/accept')) {
          console.log('Already on accept page, skipping automatic processing');
          setIsChecking(false);
          return;
        }

        // STEP 2: Check for session token first
        const activeToken = urlSessionToken || sessionToken;
        if (activeToken) {
          console.log('Found session token, validating and processing');
          await validateAndProcessSession(activeToken);
          setIsChecking(false);
          return;
        }

        // STEP 3: If no session token but we have a specific trustee ID, try to activate it
        if (trusteeId) {
          console.log('Found specific trustee invitation:', trusteeId);
          await handleSpecificTrusteeInvitation(trusteeId, termsAccepted);
          setIsChecking(false);
          return;
        }

        // STEP 3: If no specific ID, check for any pending invitations by email
        if (user.email) {
          console.log('Checking for any pending invitations for email:', user.email);
          await findAndHandlePendingInvitations();
        }
      } catch (error) {
        console.error('Error in trustee invitation checker:', error);
        toast.error('There was an error checking for trustee invitations. Please try again later.');
      } finally {
        setIsChecking(false);
      }
    };

    // Handle a specific trustee invitation by ID
    const handleSpecificTrusteeInvitation = async (trusteeId: string, termsAccepted: boolean) => {
      try {
        console.log('Handling specific trustee invitation:', trusteeId, 'Terms accepted:', termsAccepted);

        // Get the trustee record using the API endpoint
        let trusteeData: any = null;
        let fetchError: any = null;

        try {
          console.log('Fetching trustee record with ID:', trusteeId);

          // Use the API endpoint which is known to work
          const response = await fetch(`/api/trustees/invitation?id=${trusteeId}`);

          if (!response.ok) {
            console.error('Error response from invitation API:', response.status);
            fetchError = { message: `API error: ${response.status}` };
          } else {
            const data = await response.json();

            if (data && data.invitation) {
              trusteeData = data.invitation;
              console.log('Trustee record fetch response from API:', {
                found: true,
                status: trusteeData.status,
                email: trusteeData.trustee_email
              });
            } else {
              console.error('No invitation data returned from API');
              fetchError = { message: 'No invitation data returned' };
            }
          }
        } catch (error) {
          console.error('Exception fetching trustee record:', error);
          fetchError = { message: 'Failed to fetch trustee record' };
        }

        if (fetchError || !trusteeData) {
          console.error('Error fetching trustee record or trustee not found:', fetchError);
          toast.error('Could not find the trustee invitation. It may have expired.');
          clearLocalStorageData();
          return;
        }

        console.log('Trustee data found:', {
          id: trusteeData.id,
          email: trusteeData.trustee_email,
          status: trusteeData.status,
          currentUserId: trusteeData.trustee_user_id
        });

        // If already active and linked to this user, we're done
        if (trusteeData.trustee_user_id === user.id && trusteeData.status === 'active') {
          console.log('Trustee already linked to current user and active');
          clearLocalStorageData();
          return;
        }

        // If linked to another user, show error
        if (trusteeData.trustee_user_id && trusteeData.trustee_user_id !== user.id) {
          console.error('Trustee already linked to a different user:', trusteeData.trustee_user_id);
          toast.error('This invitation has already been accepted by another user');
          clearLocalStorageData();
          return;
        }

        // If terms not accepted, redirect to accept page
        if (!termsAccepted) {
          console.log('Terms not accepted, redirecting to accept page');
          if (!window.location.pathname.includes('/trustee/accept')) {
            router.push(`/trustee/accept?id=${trusteeId}`);
          }
          return;
        }

        // All checks passed, process the invitation using our unified activation endpoint
        try {
          console.log('Processing trustee invitation using unified activation endpoint:', trusteeId);

          // Get the token from cookie or localStorage if available
          const cookieToken = getCookie('trustee_session');
          const localStorageToken = localStorage.getItem('pendingTrusteeToken');
          const token = cookieToken || localStorageToken;

          // Use the unified activation endpoint
          const response = await fetch('/api/trustees/activate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              trusteeId,
              email: user.email,
              token: token || undefined
            }),
          });

          const data = await response.json();

          if (!response.ok) {
            console.error('Activation failed:', data.error);
            toast.error(data.error || 'Failed to activate trustee. Please try again.');
            return;
          }

          console.log('Activation successful:', data);

          if (data.nameUpdated) {
            console.log(`Name updated from "${data.originalName}" to "${data.updatedName}"`);
          }

          toast.success('You are now a trustee!');

          // Clear localStorage data on success
          clearLocalStorageData();

          // Redirect to dashboard if not already there
          if (!window.location.pathname.includes('/dashboard')) {
            router.push('/dashboard');
          }
        } catch (error) {
          console.error('Exception processing trustee invitation:', error);
          toast.error('Failed to process trustee invitation. Please try again.');
        }
      } catch (error) {
        console.error('Error handling specific trustee invitation:', error);
        toast.error('There was an error processing your trustee invitation. Please try again.');
        // Don't throw the error, handle it here
      }
    };

    // Find and handle any pending invitations for the user's email
    const findAndHandlePendingInvitations = async () => {
      try {
        console.log('Checking for pending invitations for email:', user.email);

        // Check for any pending invitations for this email using a direct Supabase query
        // This is simpler and less error-prone
        let pendingInvitations: any[] = [];
        let pendingError: any = null;

        try {
          console.log('Fetching pending invitations for email:', user.email.toLowerCase());

          // Use a direct query to get pending invitations
          const { data, error } = await supabase
            .from('trustees')
            .select('id, status, trustee_email')
            .eq('trustee_email', user.email.toLowerCase())
            .in('status', ['pending', 'pending_auth'])
            .order('created_at', { ascending: false });

          pendingInvitations = data || [];
          pendingError = error;

          console.log('Pending invitations query result:', {
            count: pendingInvitations.length,
            error: pendingError ? pendingError.message : 'None'
          });
        } catch (error) {
          console.error('Exception fetching pending invitations:', error);
          pendingError = { message: 'Failed to fetch pending invitations' };
        }

        if (pendingError) {
          console.error('Error checking for pending invitations:', pendingError);
          return;
        }

        if (!pendingInvitations || pendingInvitations.length === 0) {
          console.log('No pending invitations found for email:', user.email);
          return;
        }

        console.log(`Found ${pendingInvitations.length} pending invitations for email:`, user.email);

        // Process each invitation
        for (const invitation of pendingInvitations) {
          console.log('Processing invitation:', invitation.id, 'with status:', invitation.status);

          // First, link the invitation to this user if not already linked
          if (!invitation.trustee_user_id) {
            console.log('Linking invitation to user:', invitation.id, 'User ID:', user.id);

            const { error: linkError } = await supabase
              .from('trustees')
              .update({
                trustee_user_id: user.id,
                status: 'pending_auth' // Set to pending_auth until terms are accepted
              })
              .eq('id', invitation.id);

            if (linkError) {
              console.error('Error linking invitation to user:', linkError);
              continue; // Try the next invitation
            }

            console.log('Successfully linked invitation to user:', invitation.id);

            // Verify the update
            const { data: verifyData, error: verifyError } = await supabase
              .from('trustees')
              .select('trustee_user_id, status')
              .eq('id', invitation.id)
              .single();

            if (verifyError) {
              console.error('Error verifying invitation link:', verifyError);
            } else {
              console.log('Verified invitation link:', {
                id: invitation.id,
                trustee_user_id: verifyData.trustee_user_id,
                status: verifyData.status
              });
            }
          }

          // Store the first invitation in localStorage for later use
          if (pendingInvitations.indexOf(invitation) === 0) {
            localStorage.setItem('pendingTrusteeId', invitation.id);
            localStorage.setItem('pendingTrusteeEmail', invitation.trustee_email);
            console.log('Stored first invitation in localStorage:', invitation.id);
          }

          // If we have an invitation that's already linked to this user and in pending_auth status,
          // redirect to the accept page to get terms acceptance
          if (invitation.trustee_user_id === user.id && invitation.status === 'pending_auth') {
            console.log('Found pending_auth invitation, redirecting to accept page');
            router.push(`/trustee/accept?id=${invitation.id}`);
            return;
          }
        }

        // If we get here, we've processed all invitations but none required immediate action
        // Redirect to accept page for the first invitation to get terms acceptance
        console.log('Redirecting to accept page for first invitation');
        router.push(`/trustee/accept?id=${pendingInvitations[0].id}`);
      } catch (error) {
        console.error('Error finding and handling pending invitations:', error);
        // Handle the error here instead of re-throwing
        toast.error('There was an error checking for trustee invitations. Please try again later.');
      }
    };

    // Function to validate a session token and process the invitation
    const validateAndProcessSession = async (sessionToken: string) => {
      try {
        console.log(`Validating session token ${sessionToken.substring(0, 8)}...`);

        // Call the validate-session API
        const response = await fetch('/api/trustees/validate-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sessionToken
          }),
        });

        if (!response.ok) {
          console.error('Error validating session:', response.status);
          return;
        }

        const data = await response.json();

        if (!data.valid) {
          console.error('Invalid session:', data.message);
          // Clear the session cookie
          document.cookie = 'trustee_session=; path=/; max-age=0; SameSite=Lax';
          return;
        }

        console.log('Session validated successfully:', data);

        // Process the invitation using the trustee ID from the session
        const trusteeId = data.session.trustee_id;
        const termsAccepted = data.session.terms_accepted;
        const userExists = data.user_exists || false;

        if (trusteeId) {
          if (termsAccepted) {
            // If terms are accepted, process the invitation
            await handleSpecificTrusteeInvitation(trusteeId, true);
          } else {
            // If terms are not accepted, redirect to the accept page
            console.log('Terms not accepted, redirecting to accept page');
            if (!window.location.pathname.includes('/trustee/accept')) {
              router.push(`/trustee/accept?id=${trusteeId}&session=${sessionToken}`);
            }
          }

          // If the user is not logged in, we can use the user_exists flag to direct them
          if (!user && !window.location.pathname.includes('/trustee/accept')) {
            const sessionParam = sessionToken ? `&session=${sessionToken}` : '';

            if (userExists) {
              console.log('User exists but not logged in, redirecting to login');
              router.push(`/login?redirect=/dashboard&trusteeId=${trusteeId}${sessionParam}`);
            } else {
              console.log('User does not exist, redirecting to register');
              router.push(`/register?redirect=/dashboard&trusteeId=${trusteeId}${sessionParam}`);
            }
          }
        }
      } catch (error) {
        console.error('Error validating session:', error);
      }
    };

    // Note: We've replaced the activateTrustee function with direct API calls in handleSpecificTrusteeInvitation

    // Clear all trustee-related localStorage data and cookies
    const clearLocalStorageData = () => {
      // Clear localStorage
      localStorage.removeItem('pendingTrusteeId');
      localStorage.removeItem('pendingTrusteeEmail');
      localStorage.removeItem('pendingTrusteeAcceptedAt');
      localStorage.removeItem('trusteeTermsAccepted');
      localStorage.removeItem('pendingTrusteeToken');

      // Clear session cookie
      document.cookie = 'trustee_session=; path=/; max-age=0; SameSite=Lax';
    };

    // Run the check
    checkForPendingInvitations();
  }, [user, loading, router]);

  // This component doesn't render anything
  return null;
}
