
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Lightbulb, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase-unified';

interface Asset {
  id: number;
  name: string;
  category: string;
  value: string;
}

interface Recommendation {
  title: string;
  description: string;
}

const EstateRecommendations = ({ assets }: { assets: Asset[] }) => {
  const [context, setContext] = useState<string>('');
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['estateRecommendations', assets.map(a => a.id).join(',')],
    queryFn: async () => {
      try {
        const { data, error } = await supabase.functions.invoke('estate-recommendations', {
          body: { assets, context }
        });
        
        if (error) throw error;
        return data.recommendations;
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        toast.error('Failed to get estate recommendations');
        throw error;
      }
    },
    enabled: assets.length > 0,
  });

  const handleRefresh = () => {
    refetch();
    toast.info('Refreshing recommendations...');
  };
  
  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Estate Planning Recommendations</CardTitle>
            <CardDescription>
              AI-powered recommendations based on your assets and estate
            </CardDescription>
          </div>
          <Button variant="outline" size="icon" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center p-6">
            <div className="animate-pulse text-center">
              <div className="h-6 w-32 bg-gray-200 rounded mx-auto mb-2"></div>
              <div className="h-4 w-48 bg-gray-200 rounded mx-auto"></div>
            </div>
          </div>
        ) : error ? (
          <div className="text-center p-6 text-red-500">
            <p>Failed to load recommendations</p>
            <Button variant="outline" className="mt-2" onClick={handleRefresh}>Try Again</Button>
          </div>
        ) : data && data.length > 0 ? (
          <div className="space-y-4">
            {data.map((recommendation: Recommendation, index: number) => (
              <div key={index} className="flex p-4 bg-blue-50 rounded-lg">
                <div className="mr-4 mt-1">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <Lightbulb className="h-4 w-4 text-blue-700" />
                  </div>
                </div>
                <div>
                  <h4 className="font-medium">{recommendation.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-6 text-gray-500">
            <p>No recommendations available yet</p>
            <Button variant="outline" className="mt-2" onClick={handleRefresh}>
              Generate Recommendations
            </Button>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex-col items-start">
        <p className="text-sm text-gray-600 mb-2">
          Provide additional context to get more tailored recommendations:
        </p>
        <textarea 
          className="w-full p-2 border rounded-md text-sm" 
          rows={2}
          placeholder="E.g., I have two children and want to minimize estate taxes"
          value={context}
          onChange={(e) => setContext(e.target.value)}
        />
        <Button className="mt-2" onClick={handleRefresh} disabled={isLoading}>
          Update Recommendations
        </Button>
      </CardFooter>
    </Card>
  );
};

export default EstateRecommendations;
