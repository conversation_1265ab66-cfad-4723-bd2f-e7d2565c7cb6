"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Loader2, CreditCard } from 'lucide-react';
import { toast } from 'sonner';

interface VerificationSuccessProps {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    emailVerified: boolean;
  };
}

export default function VerificationSuccess({ user }: VerificationSuccessProps) {
  const router = useRouter();
  const { createCheckout } = useSubscription();
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [selectedTier, setSelectedTier] = useState<string | null>(null);

  useEffect(() => {
    // Check if a premium tier was selected
    const tier = localStorage.getItem('selectedTier');
    setSelectedTier(tier);

    // Clean up localStorage
    localStorage.removeItem('selectedTier');
  }, []);

  const handleProceedToPayment = async () => {
    if (selectedTier !== 'premium') return;

    setIsProcessingPayment(true);
    try {
      const checkoutUrl = await createCheckout('premium');
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      } else {
        toast.error('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout:', error);
      toast.error('Failed to proceed to payment');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handleSkipPayment = () => {
    // User chose to skip payment and use free tier
    toast.success('Welcome to Legalock! You can upgrade anytime from your dashboard.');
    router.push('/dashboard');
  };

  const handleProceedToDashboard = () => {
    // User signed up for free tier
    toast.success('Welcome to Legalock!');
    router.push('/dashboard');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Email Verified Successfully!
          </CardTitle>
          <CardDescription>
            Welcome to Legalock, {user.firstName}! Your account has been created.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {selectedTier === 'premium' ? (
            <>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <CreditCard className="h-5 w-5 text-blue-600 mr-2" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-900">
                      Complete Your Premium Subscription
                    </h3>
                    <p className="text-xs text-blue-700 mt-1">
                      You selected the Legacy Preserver plan ($29.99/year). Complete your payment to unlock all premium features.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleProceedToPayment}
                  disabled={isProcessingPayment}
                  className="w-full"
                >
                  {isProcessingPayment ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Complete Payment
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleSkipPayment}
                  variant="outline"
                  className="w-full"
                  disabled={isProcessingPayment}
                >
                  Skip for Now (Use Free Plan)
                </Button>
              </div>

              <p className="text-xs text-gray-500 text-center">
                You can upgrade to premium anytime from your dashboard.
              </p>
            </>
          ) : (
            <>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-center">
                  <h3 className="text-sm font-medium text-green-900">
                    Essential Legacy Plan (Free)
                  </h3>
                  <p className="text-xs text-green-700 mt-1">
                    You're all set to start managing your digital legacy!
                  </p>
                </div>
              </div>

              <Button
                onClick={handleProceedToDashboard}
                className="w-full"
              >
                Go to Dashboard
              </Button>

              <p className="text-xs text-gray-500 text-center">
                You can upgrade to premium features anytime from your dashboard.
              </p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
