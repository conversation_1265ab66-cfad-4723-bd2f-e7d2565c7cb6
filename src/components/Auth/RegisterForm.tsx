
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { useAuth } from '@/context/auth-context';
import { Eye, EyeOff } from 'lucide-react';

const formSchema = z.object({
  firstName: z.string().min(2, { message: 'First name must be at least 2 characters' }),
  lastName: z.string().min(2, { message: 'Last name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  terms: z.boolean().refine(val => val === true, { message: 'You must accept the terms and conditions' })
});

type FormValues = z.infer<typeof formSchema>;

const RegisterForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signUp } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [trusteeId, setTrusteeId] = useState<string | null>(null);
  const [selectedTier, setSelectedTier] = useState<'free' | 'premium'>('free');

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      terms: false,
    },
  });

  // State to track if fields should be read-only
  const [isEmailReadOnly, setIsEmailReadOnly] = useState(false);
  const [isNameReadOnly, setIsNameReadOnly] = useState(false);
  const [inviterFirstName, setInviterFirstName] = useState('');
  const [inviterLastName, setInviterLastName] = useState('');

  // Get email, trustee ID, tier, and name from URL parameters
  useEffect(() => {
    if (!searchParams) return;

    const email = searchParams.get('email');
    const trustee = searchParams.get('trustee');
    const inviteId = searchParams.get('id'); // For trustee invitation links
    const firstName = searchParams.get('firstName');
    const lastName = searchParams.get('lastName');
    const trusteeId = searchParams.get('trusteeId');
    const tier = searchParams.get('tier') as 'free' | 'premium' | null;

    // Log all parameters for debugging
    console.log('URL parameters:', { email, trustee, inviteId, firstName, lastName, trusteeId, tier });

    // Set the selected tier
    if (tier && (tier === 'free' || tier === 'premium')) {
      setSelectedTier(tier);
      console.log('Selected tier:', tier);
    }

    // Check if this is a trustee invitation (via any of the trustee-related parameters)
    if (trustee || inviteId || trusteeId) {
      const id = trusteeId || trustee || inviteId;
      console.log('Detected trustee invitation with ID:', id);
      setTrusteeId(id);
      // If coming from trustee invitation, email should be read-only
      setIsEmailReadOnly(true);

      // If first and last name are provided, make them read-only
      if (firstName && lastName) {
        setIsNameReadOnly(true);
        setInviterFirstName(firstName);
        setInviterLastName(lastName);
        form.setValue('firstName', firstName);
        form.setValue('lastName', lastName);
        console.log(`Set name fields to read-only with values: ${firstName} ${lastName}`);
      } else {
        console.log('First and last name not provided in URL parameters');
      }

      // Store trustee information in localStorage for activation after signup
      if (id) {
        localStorage.setItem('pendingTrusteeId', id);
        console.log('Stored trustee ID in localStorage:', id);
      }
    }

    if (email) {
      form.setValue('email', email);
      // Store email in localStorage for trustee activation
      if (trustee || inviteId || trusteeId) {
        localStorage.setItem('pendingTrusteeEmail', email);
        console.log('Stored trustee email in localStorage:', email);
      }
    }

    // Mark terms as accepted if this is a trustee invitation
    if (trustee || inviteId || trusteeId) {
      localStorage.setItem('trusteeTermsAccepted', 'true');
      console.log('Marked trustee terms as accepted in localStorage');
    }
  }, [searchParams, form]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      // If this is a trustee registration, ensure we have all the necessary data in localStorage
      if (trusteeId) {
        // Log the trustee ID for debugging
        console.log('Processing trustee registration with ID:', trusteeId);

        // Ensure we have the trustee terms accepted flag set
        localStorage.setItem('trusteeTermsAccepted', 'true');
        console.log('User has accepted trustee terms, will activate after verification');

        // The TrusteeInvitationChecker will handle the activation after signup
      }

      // Store the selected tier for post-signup processing
      if (selectedTier) {
        localStorage.setItem('selectedTier', selectedTier);
      }

      await signUp(
        data.email,
        data.password,
        data.firstName,
        data.lastName,
        false, // autoVerify
        trusteeId // Pass the trustee invitation ID if present
      );

      // If premium tier is selected and this is not a trustee signup, redirect to payment
      if (selectedTier === 'premium' && !trusteeId) {
        // The signup was successful, now redirect to payment
        // This will be handled in the verification success flow
        console.log('Premium tier selected, payment will be handled after email verification');
      }

      // Redirect is handled in the AuthContext
    } catch (error) {
      console.error('Registration error:', error);
      // Error is handled in the AuthContext
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div>
      {/* Display selected tier */}
      {selectedTier && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-blue-900">
                Selected Plan: {selectedTier === 'free' ? 'Essential Legacy (Free)' : 'Legacy Preserver ($29.99/year)'}
              </h3>
              <p className="text-xs text-blue-700 mt-1">
                {selectedTier === 'premium'
                  ? 'You will be redirected to payment after email verification.'
                  : 'You can upgrade anytime from your dashboard.'}
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => router.push('/signup/select-tier')}
            >
              Change Plan
            </Button>
          </div>
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John"
                      {...field}
                      readOnly={isNameReadOnly}
                      className={isNameReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}
                    />
                  </FormControl>
                  {isNameReadOnly && (
                    <p className="text-xs text-gray-500 mt-1">
                      This name is pre-filled from your trustee invitation and cannot be changed.
                    </p>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Doe"
                      {...field}
                      readOnly={isNameReadOnly}
                      className={isNameReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}
                    />
                  </FormControl>
                  {isNameReadOnly && (
                    <p className="text-xs text-gray-500 mt-1">
                      This name is pre-filled from your trustee invitation and cannot be changed.
                    </p>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    type="email"
                    {...field}
                    readOnly={isEmailReadOnly}
                    className={isEmailReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}
                  />
                </FormControl>
                {isEmailReadOnly && (
                  <p className="text-xs text-gray-500 mt-1">
                    This email is pre-filled from your trustee invitation and cannot be changed.
                  </p>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="••••••••"
                      type={showPassword ? "text" : "password"}
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-500"
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="terms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-sm font-normal">
                    I agree to the{' '}
                    <Link href="/terms" className="text-primary hover:underline">
                      terms of service
                    </Link>{' '}
                    and{' '}
                    <Link href="/privacy" className="text-primary hover:underline">
                      privacy policy
                    </Link>
                  </FormLabel>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                  Creating account...
                </span>
              ) : (
                'Create Account'
              )}
            </Button>
          </div>
        </form>
      </Form>

      <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          Already have an account?{' '}
          <Link href="/login" className="text-primary hover:underline">
            Sign in
          </Link>
        </p>
      </div>
    </div>
  );
};

export default RegisterForm;
