"use client";

import React, { useState, useRef } from 'react';
import { Upload, File, X, AlertCircle, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatFileSize } from '@/lib/utils';

interface AssetImageUploaderProps {
  onImageSelected: (file: File) => void;
  onImageUploaded: (url: string) => void;
  maxSizeMB?: number;
}

export const AssetImageUploader = ({
  onImageSelected,
  onImageUploaded,
  maxSizeMB = 5, // Default max size: 5MB
}: AssetImageUploaderProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  const acceptedFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  const validateFile = (file: File): boolean => {
    // Check file size
    if (file.size > maxSizeBytes) {
      setError(`File size exceeds the maximum limit of ${maxSizeMB}MB`);
      return false;
    }

    // Check file type
    if (!acceptedFileTypes.includes(file.type)) {
      setError(`File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)`);
      return false;
    }

    setError(null);
    return true;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        setPreviewUrl(URL.createObjectURL(file));
        onImageSelected(file);
        uploadImage(file);
      } else {
        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        setPreviewUrl(URL.createObjectURL(file));
        onImageSelected(file);
        uploadImage(file);
      }
    }
  };

  const clearSelectedFile = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const uploadImage = async (file: File) => {
    setIsUploading(true);
    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bucket', 'asset-images');
      formData.append('path', `${Date.now()}_${file.name}`);

      // Upload the file
      const response = await fetch('/api/storage/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      onImageUploaded(data.fullPath);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="w-full">
      {!selectedFile ? (
        <div
          className={`border-2 border-dashed ${error ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:bg-gray-50'} rounded-lg p-4 text-center cursor-pointer transition-colors`}
          onClick={() => fileInputRef.current?.click()}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {error ? (
            <AlertCircle className="mx-auto h-8 w-8 text-red-400" />
          ) : (
            <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
          )}
          <p className={`mt-2 text-sm ${error ? 'text-red-600 font-medium' : 'text-gray-600'}`}>
            {error || 'Drag and drop an image, or click to select'}
          </p>
          <p className="mt-1 text-xs text-gray-500">
            JPEG, PNG, GIF, WEBP up to {maxSizeMB}MB
          </p>
          <input
            type="file"
            className="hidden"
            onChange={handleFileChange}
            ref={fileInputRef}
            accept={acceptedFileTypes.join(',')}
          />
        </div>
      ) : (
        <div className="border rounded-lg p-2">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <File className="h-4 w-4 text-primary mr-2" />
              <div>
                <p className="text-sm font-medium truncate max-w-[200px] md:max-w-xs">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(selectedFile.size)}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
              onClick={clearSelectedFile}
              disabled={isUploading}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Remove file</span>
            </Button>
          </div>
          
          {previewUrl && (
            <div className="relative h-32 w-full bg-gray-100 overflow-hidden rounded">
              <img 
                src={previewUrl} 
                alt="Preview" 
                className="w-full h-full object-cover"
              />
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="animate-spin w-6 h-6 border-2 border-white border-t-transparent rounded-full"></div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
