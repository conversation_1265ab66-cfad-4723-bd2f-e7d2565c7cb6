"use client";

import React, { useEffect, useState, useRef } from 'react';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartData, ChartOptions } from 'chart.js';
import { Asset } from '@/types/database.types';
import {
  ASSET_CATEGORIES
} from '@/types/asset.types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Bar<PERSON><PERSON>, DollarSign, PieChart } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

// Register the required Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

interface AssetPieChartProps {
  assets: Asset[];
  selectedCurrency: string;
  currencySymbol: string;
  convertCurrency: (value: number, fromCurrency: string, toCurrency: string) => number;
  onCategorySelect?: (category: string) => void;
}

interface ChartDataItem {
  category: string;
  value: number;
  count: number;
}

export default function AssetPieChart({ assets, selectedCurrency, currencySymbol, convertCurrency, onCategorySelect }: AssetPieChartProps) {
  const [categoryData, setCategoryData] = useState<ChartDataItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [chartData, setChartData] = useState<ChartData<'pie'>>({
    labels: [],
    datasets: [
      {
        data: [],
        backgroundColor: [],
        borderColor: [],
        borderWidth: 1,
      },
    ],
  });

  // Define chart colors
  const backgroundColors = [
    'rgba(54, 162, 235, 0.6)',
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(75, 192, 192, 0.6)',
    'rgba(153, 102, 255, 0.6)',
    'rgba(255, 159, 64, 0.6)',
    'rgba(199, 199, 199, 0.6)',
    'rgba(83, 102, 255, 0.6)',
    'rgba(78, 205, 196, 0.6)',
    'rgba(255, 99, 71, 0.6)',
  ];

  const borderColors = [
    'rgba(54, 162, 235, 1)',
    'rgba(255, 99, 132, 1)',
    'rgba(255, 206, 86, 1)',
    'rgba(75, 192, 192, 1)',
    'rgba(153, 102, 255, 1)',
    'rgba(255, 159, 64, 1)',
    'rgba(199, 199, 199, 1)',
    'rgba(83, 102, 255, 1)',
    'rgba(78, 205, 196, 1)',
    'rgba(255, 99, 71, 1)',
  ];

  // Chart options
  const options: ChartOptions<'pie'> = {
    responsive: true,
    onClick: (event, elements) => {
      if (elements && elements.length > 0) {
        const clickedIndex = elements[0].index;
        const category = chartData.labels?.[clickedIndex] as string;

        if (category) {
          setSelectedCategory(category);
          if (onCategorySelect) {
            // Find the original category value (not label) to filter by
            const categoryValue = ASSET_CATEGORIES.find(c => c.label === category)?.value;
            if (categoryValue) {
              onCategorySelect(categoryValue);
            }
          }
        }
      }
    },
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15,
          generateLabels: (chart) => {
            const datasets = chart.data.datasets;
            return chart.data.labels?.map((label, i) => ({
              text: label as string,
              fillStyle: datasets[0].backgroundColor?.[i] as string,
              strokeStyle: datasets[0].borderColor?.[i] as string,
              lineWidth: 1,
              hidden: false,
              index: i
            })) || [];
          },
        },
        onClick: (e, legendItem, legend) => {
          const index = legendItem.index;
          const category = chartData.labels?.[index] as string;

          if (category) {
            setSelectedCategory(category);
            if (onCategorySelect) {
              // Find the original category value (not label) to filter by
              const categoryValue = ASSET_CATEGORIES.find(c => c.label === category)?.value;
              if (categoryValue) {
                onCategorySelect(categoryValue);
              }
            }
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw as number;
            const count = categoryData[context.dataIndex]?.count || 0;
            return [`${label}: ${currencySymbol}${value.toLocaleString()}`, `Items: ${count}`];
          }
        }
      }
    },
  };

  // Get category label from value
  const getCategoryLabel = (categoryValue: string) => {
    const category = ASSET_CATEGORIES.find(c => c.value === categoryValue);
    return category ? category.label : categoryValue;
  };

  // Prepare chart data
  useEffect(() => {
    if (!assets || assets.length === 0) {
      setChartData({
        labels: ['No Assets'],
        datasets: [
          {
            data: [1],
            backgroundColor: ['rgba(200, 200, 200, 0.6)'],
            borderColor: ['rgba(200, 200, 200, 1)'],
            borderWidth: 1,
          },
        ],
      });
      setCategoryData([]);
      return;
    }

    // Group assets by category
    const categoryMap = new Map<string, ChartDataItem>();

    assets.forEach(asset => {
      const categoryValue = asset.category;
      const categoryLabel = getCategoryLabel(categoryValue);
      const assetValue = asset.value || 0;
      // Convert asset value to selected currency
      const convertedValue = convertCurrency(assetValue, asset.currency || 'USD', selectedCurrency);

      if (categoryMap.has(categoryLabel)) {
        const item = categoryMap.get(categoryLabel)!;
        item.value += convertedValue;
        item.count += 1;
      } else {
        categoryMap.set(categoryLabel, {
          category: categoryLabel,
          value: convertedValue,
          count: 1,
        });
      }
    });

    // Convert to arrays for chart
    const categories = Array.from(categoryMap.values());

    // Sort by value
    categories.sort((a, b) => b.value - a.value);

    // Store category data for the summary section
    setCategoryData(categories);

    const labels = categories.map(item => item.category);
    const data = categories.map(item => item.value);

    // Assign colors
    const bgColors = labels.map((_, index) => backgroundColors[index % backgroundColors.length]);
    const bdColors = labels.map((_, index) => borderColors[index % borderColors.length]);

    setChartData({
      labels,
      datasets: [
        {
          data,
          backgroundColor: bgColors,
          borderColor: bdColors,
          borderWidth: 1,
        },
      ],
    });
  }, [assets, selectedCurrency, convertCurrency]);

  // Calculate total value
  const totalValue = assets.reduce((sum, asset) => {
    const assetValue = asset.value || 0;
    const convertedValue = convertCurrency(assetValue, asset.currency || 'USD', selectedCurrency);
    return sum + convertedValue;
  }, 0);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 h-full">
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <PieChart className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Asset Distribution</h3>
            <p className="feature-card-subtitle">By category in {selectedCurrency}</p>
          </div>
        </div>
        {selectedCategory && (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer"
            onClick={() => {
              setSelectedCategory(null);
              if (onCategorySelect) onCategorySelect('all');
            }}
          >
            Clear Filter
          </Badge>
        )}
      </div>

      <div className="h-[250px] flex items-center justify-center">
        {assets.length === 0 ? (
          <p className="text-gray-500 text-center">
            Add assets to see your distribution chart
          </p>
        ) : (
          <Pie data={chartData} options={options} />
        )}
      </div>

      {assets.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center mb-3">
            <p className="text-sm font-medium">Total Value</p>
            <p className="text-xl font-bold text-blue-700">
              {currencySymbol}{totalValue.toLocaleString()}
            </p>
          </div>

          {/* Category Value Summaries */}
          <div className="mt-4 space-y-3">
            {categoryData.slice(0, 5).map((category, index) => {
              const percentage = totalValue > 0 ? (category.value / totalValue) * 100 : 0;
              return (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: backgroundColors[index % backgroundColors.length] }}
                      ></div>
                      <span className="text-sm font-medium">{category.category}</span>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">{currencySymbol}{category.value.toLocaleString()}</span>
                      <span className="text-gray-500 ml-2">({category.count} items)</span>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-1.5" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{percentage.toFixed(1)}%</span>
                    <span onClick={() => {
                      if (onCategorySelect) {
                        const categoryValue = ASSET_CATEGORIES.find(c => c.label === category.category)?.value;
                        if (categoryValue) onCategorySelect(categoryValue);
                      }
                    }} className="text-blue-600 cursor-pointer hover:underline">
                      View Assets
                    </span>
                  </div>
                </div>
              );
            })}

            {categoryData.length > 5 && (
              <div className="text-center mt-2">
                <span className="text-xs text-gray-500">
                  + {categoryData.length - 5} more categories
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
