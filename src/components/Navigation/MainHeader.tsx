
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Shield } from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';

const MainHeader = () => {
  const pathname = usePathname();
  const isLandingPage = pathname === '/';

  return (
    <header className="w-full bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center gap-2">
              <Shield className="h-8 w-8 text-primary" />
              <span className="text-xl font-bold text-gray-900">Legalock</span>
            </Link>
          </div>

          {/* Navigation - Only shown on landing page */}
          {isLandingPage && (
            <NavigationMenu className="hidden md:flex">
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Features</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      <ListItem href="#" title="Asset Management" icon="Shield">
                        Create a comprehensive inventory of your digital and physical assets
                      </ListItem>
                      <ListItem href="#" title="Digital Vault" icon="Vault">
                        Securely store sensitive documents for your trustees
                      </ListItem>
                      <ListItem href="#" title="Time Capsule" icon="Clock">
                        Schedule future messages to loved ones
                      </ListItem>
                      <ListItem href="#" title="Will Writing" icon="FileText">
                        Document your final wishes with our step-by-step guide
                      </ListItem>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Pricing</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                      <li className="row-span-3">
                        <NavigationMenuLink asChild>
                          <a
                            className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                            href="/pricing"
                          >
                            <div className="mb-2 mt-4 text-lg font-medium">
                              Choose Your Plan
                            </div>
                            <p className="text-sm leading-tight text-muted-foreground">
                              Select the perfect plan for your digital legacy needs
                            </p>
                          </a>
                        </NavigationMenuLink>
                      </li>
                      <ListItem href="/pricing#free" title="Essential Legacy" icon="Shield">
                        Free plan with basic features for everyone
                      </ListItem>
                      <ListItem href="/pricing#premium" title="Legacy Preserver" icon="Crown">
                        Premium plan with advanced features and unlimited access
                      </ListItem>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/terms" className={navigationMenuTriggerStyle()}>
                    Terms
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          )}

          {/* Auth Buttons */}
          <div className="flex items-center gap-4">
            {isLandingPage ? (
              <>
                <Button variant="outline" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/signup/select-tier">Sign Up</Link>
                </Button>
              </>
            ) : (
              <Button variant="outline" asChild>
                <Link href="/">Back to Home</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { icon: string; title: string }
>(({ className, title, children, icon, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItem.displayName = "ListItem";

export default MainHeader;
