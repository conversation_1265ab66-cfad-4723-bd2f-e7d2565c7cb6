"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Menu, LogOut, Users, Settings, CreditCard, Shield } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAuth } from '@/context/auth-context';
import { toast } from 'sonner';

const Header = () => {
  const { user, signOut } = useAuth();
  const [isTrustee, setIsTrustee] = useState<boolean | null>(null);

  // Generate user initials for avatar
  const getUserInitials = () => {
    if (!user) return 'U';

    // Try to get initials from firstName and lastName
    if (user.firstName && user.lastName) {
      return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
    }

    // Fallback to email
    if (user.email) {
      // Get first letter of email and first letter after @ or first letter of domain
      const emailParts = user.email.split('@');
      if (emailParts.length === 2) {
        return `${emailParts[0].charAt(0)}${emailParts[1].charAt(0)}`.toUpperCase();
      }
      return user.email.charAt(0).toUpperCase();
    }

    // Ultimate fallback
    return 'U';
  };

  useEffect(() => {
    if (user) {
      checkTrusteeStatus();
    }
  }, [user]);

  const checkTrusteeStatus = async () => {
    try {
      if (!user) return;

      // Use the API endpoint to check trustee status
      const response = await fetch('/api/trustees/check-status');

      if (!response.ok) {
        throw new Error('Failed to check trustee status');
      }

      const data = await response.json();
      setIsTrustee(data.isTrustee);

      console.log('Trustee status:', data.isTrustee ? 'Is a trustee' : 'Not a trustee');
      if (data.isTrustee) {
        console.log(`User is a trustee for ${data.trusteeCount} people`);
      }
    } catch (error) {
      console.error('Error checking trustee status:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success('Successfully signed out');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  return (
    <header className="h-20 border-b border-gray-200 bg-white flex items-center px-4 sm:px-6 relative">
      <div className="absolute left-1/2 transform -translate-x-1/2">
        <Link href="/dashboard" className="flex items-center">
          <Image
            src="/images/Legalock-logo.svg"
            alt="Legalock Logo"
            width={160}
            height={50}
            style={{ height: 'auto' }}
            priority
          />
        </Link>
      </div>

      <div className="ml-auto flex items-center space-x-4">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" asChild>
                <Link href={isTrustee ? "/trustee/dashboard" : "/trustee/not-a-trustee"} className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Trustee Access
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isTrustee ? "Access your trustee responsibilities" : "Learn about being a trustee"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" asChild>
                <Link href="/subscription" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Subscription
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Manage your subscription plan</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" asChild>
                <Link href="/settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Manage your account settings</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>



        <Button
          variant="outline"
          size="sm"
          onClick={handleSignOut}
          className="flex items-center gap-2"
        >
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>

        <Avatar>
          <AvatarImage src="" />
          <AvatarFallback className="bg-primary text-primary-foreground">{getUserInitials()}</AvatarFallback>
        </Avatar>
      </div>
    </header>
  );
};

export default Header;
