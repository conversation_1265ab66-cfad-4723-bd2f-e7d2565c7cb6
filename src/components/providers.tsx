"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/context/auth-context";
import { SubscriptionProvider } from "@/context/SubscriptionContext";
import { useState, useEffect } from "react";
import { initSupabaseClient } from "@/lib/supabase-init";

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());

  // Initialize Supabase client on component mount
  useEffect(() => {
    // Initialize the Supabase client
    const client = initSupabaseClient();
    if (client) {
      console.log("Supabase client initialized in providers");
    } else {
      console.error("Failed to initialize Supabase client in providers");
    }
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <SubscriptionProvider>
          {children}
        </SubscriptionProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
