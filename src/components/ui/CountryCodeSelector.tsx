"use client";

import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Country codes with flags
const COUNTRY_CODES = [
  { code: '+1', country: 'US', flag: '🇺🇸', name: 'United States' },
  { code: '+1', country: 'CA', flag: '🇨🇦', name: 'Canada' },
  { code: '+44', country: 'GB', flag: '🇬🇧', name: 'United Kingdom' },
  { code: '+61', country: 'AU', flag: '🇦🇺', name: 'Australia' },
  { code: '+33', country: 'FR', flag: '🇫🇷', name: 'France' },
  { code: '+49', country: 'DE', flag: '🇩🇪', name: 'Germany' },
  { code: '+81', country: 'JP', flag: '🇯🇵', name: 'Japan' },
  { code: '+86', country: 'CN', flag: '🇨🇳', name: 'China' },
  { code: '+91', country: 'IN', flag: '🇮🇳', name: 'India' },
  { code: '+55', country: 'BR', flag: '🇧🇷', name: 'Brazil' },
  { code: '+52', country: 'MX', flag: '🇲🇽', name: 'Mexico' },
  { code: '+34', country: 'ES', flag: '🇪🇸', name: 'Spain' },
  { code: '+39', country: 'IT', flag: '🇮🇹', name: 'Italy' },
  { code: '+7', country: 'RU', flag: '🇷🇺', name: 'Russia' },
  { code: '+82', country: 'KR', flag: '🇰🇷', name: 'South Korea' },
  { code: '+31', country: 'NL', flag: '🇳🇱', name: 'Netherlands' },
  { code: '+46', country: 'SE', flag: '🇸🇪', name: 'Sweden' },
  { code: '+41', country: 'CH', flag: '🇨🇭', name: 'Switzerland' },
  { code: '+64', country: 'NZ', flag: '🇳🇿', name: 'New Zealand' },
  { code: '+27', country: 'ZA', flag: '🇿🇦', name: 'South Africa' },
];

interface CountryCodeSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
}

export default function CountryCodeSelector({ value, onValueChange, disabled }: CountryCodeSelectorProps) {
  return (
    <Select 
      value={value || '+1'} 
      onValueChange={onValueChange} 
      disabled={disabled}
    >
      <SelectTrigger className="w-[100px]">
        <SelectValue placeholder="Code" />
      </SelectTrigger>
      <SelectContent>
        {COUNTRY_CODES.map((country) => (
          <SelectItem key={`${country.code}-${country.country}`} value={country.code}>
            <div className="flex items-center">
              <span className="mr-2">{country.flag}</span>
              <span>{country.code}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Phone validation functions
export const validatePhoneNumber = (phone: string, countryCode: string): boolean => {
  if (!phone) return true; // Empty is valid (optional)
  
  // Remove any non-digit characters except for the leading +
  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  // Basic validation patterns by country code
  const patterns: Record<string, RegExp> = {
    '+1': /^\+?1?\d{10}$/, // US/Canada: +1 followed by 10 digits
    '+44': /^\+?44?\d{10}$/, // UK: +44 followed by 10 digits
    '+61': /^\+?61?\d{9}$/, // Australia: +61 followed by 9 digits
    '+33': /^\+?33?\d{9}$/, // France: +33 followed by 9 digits
    '+49': /^\+?49?\d{10,11}$/, // Germany: +49 followed by 10-11 digits
    '+81': /^\+?81?\d{10}$/, // Japan: +81 followed by 10 digits
    '+86': /^\+?86?\d{11}$/, // China: +86 followed by 11 digits
    '+91': /^\+?91?\d{10}$/, // India: +91 followed by 10 digits
  };
  
  // If we have a specific pattern for this country code, use it
  if (patterns[countryCode]) {
    return patterns[countryCode].test(cleanPhone);
  }
  
  // Default validation: country code + at least 7 digits
  return /^\+?\d{1,4}\d{7,}$/.test(cleanPhone);
};

// Format phone number for display
export const formatPhoneNumber = (phone: string, countryCode: string): string => {
  if (!phone) return '';
  
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format based on country code
  if (countryCode === '+1') {
    // US/Canada: (XXX) XXX-XXXX
    if (digits.length === 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    }
  }
  
  // Default: just return the cleaned number with the country code
  return `${countryCode} ${digits}`;
};
