import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-conditional';
import { supabaseAdmin } from '@/lib/supabase-unified';
import { SubscriptionTier, getSubscriptionLimits } from '@/config/subscription-limits';

/**
 * Middleware to check if a user has access to a premium feature
 * @param request The Next.js request object
 * @param featureName The name of the feature to check
 * @returns NextResponse or null if the user has access
 */
export async function checkPremiumFeatureAccess(
  request: NextRequest,
  featureName: 'vault' | 'timeCapsule' | 'prioritySupport'
): Promise<NextResponse | null> {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Get the user's profile from Supabase
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single();

    // If there's an error or no profile, assume free tier
    const tier = (profile?.subscription_tier as SubscriptionTier) || 'free';

    // Get the limits for the user's tier
    const limits = getSubscriptionLimits(tier);

    // Check if the feature is available for the user's tier
    if (!limits.features[featureName]) {
      return NextResponse.redirect(new URL('/pricing?feature=' + featureName, request.url));
    }

    // User has access to the feature
    return null;
  } catch (error) {
    console.error('Error in subscription check middleware:', error);
    // Default to allowing access in case of errors
    return null;
  }
}

/**
 * Check if a user has reached a specific limit
 * @param userId The user's ID
 * @param limitType The type of limit to check (e.g., 'trustees', 'assets')
 * @param table The database table to check
 * @returns Whether the user has reached the limit
 */
export async function checkResourceLimit(
  userId: string,
  limitType: 'trustees' | 'assets' | 'contacts' | 'service_sunset' | 'time_capsules',
  table: string
): Promise<{ hasReachedLimit: boolean; currentCount: number; maxAllowed: number }> {
  try {
    // Get the user's profile from Supabase
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    // If there's an error or no profile, assume free tier
    const tier = (profile?.subscription_tier as SubscriptionTier) || 'free';

    // Get the limits for the user's tier
    const limits = getSubscriptionLimits(tier);

    // Map limitType to the corresponding limit in the TierLimits interface
    const limitMapping = {
      trustees: 'maxTrustees',
      assets: 'maxAssets',
      contacts: 'maxContacts',
      service_sunset: 'maxServiceSunset',
      time_capsules: 'maxTimeCapsules',
    } as const;

    const limitKey = limitMapping[limitType];
    const maxAllowed = limits[limitKey];

    // If the limit is Infinity, the user hasn't reached the limit
    if (maxAllowed === Infinity) {
      return { hasReachedLimit: false, currentCount: 0, maxAllowed: Infinity };
    }

    // Count the number of resources the user has
    const { count, error: countError } = await supabaseAdmin
      .from(table)
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      console.error(`Error counting ${table}:`, countError);
      return { hasReachedLimit: false, currentCount: 0, maxAllowed };
    }

    const currentCount = count || 0;
    const hasReachedLimit = currentCount >= maxAllowed;

    return { hasReachedLimit, currentCount, maxAllowed };
  } catch (error) {
    console.error('Error checking resource limit:', error);
    // Default to not limiting in case of errors
    return { hasReachedLimit: false, currentCount: 0, maxAllowed: Infinity };
  }
}
