import { Resend } from 'resend';
import { getVerificationEmailTemplate } from '@/emails/verification-email';
import { supabaseAdmin } from '@/lib/supabase-unified';

// Re-export supabaseAdmin for backward compatibility
export { supabaseAdmin };

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * Generate a secure random string of specified length using Web Crypto API
 */
export function generateRandomString(length: number): string {
  const array = new Uint8Array(Math.ceil(length / 2));
  crypto.getRandomValues(array);
  return Array.from(array)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
    .slice(0, length);
}

/**
 * Generate a 6-digit verification code
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Generate a session token
 */
export function generateSessionToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Send a verification email with a 6-digit code
 */
export async function sendVerificationEmail(email: string, code: string): Promise<boolean> {
  try {
    const { error } = await resend.emails.send({
      from: 'Legalock <<EMAIL>>',
      to: [email],
      subject: 'Verify your Legalock account',
      html: getVerificationEmailTemplate(email, code),
    });

    if (error) {
      console.error('Error sending verification email:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception sending verification email:', error);
    return false;
  }
}

/**
 * Hash a password using Web Crypto API
 */
export async function hashPassword(password: string): Promise<string> {
  // Convert password to bytes
  const encoder = new TextEncoder();
  const passwordData = encoder.encode(password);

  // Generate a random salt
  const salt = crypto.getRandomValues(new Uint8Array(16));
  const saltHex = Array.from(salt).map(b => b.toString(16).padStart(2, '0')).join('');

  // Use Web Crypto API to hash the password
  // First create a key from the password
  const passwordKey = await crypto.subtle.importKey(
    'raw',
    passwordData,
    { name: 'PBKDF2' },
    false,
    ['deriveBits']
  );

  // Then derive bits using PBKDF2
  const iterations = 10000;
  const derivedBits = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt: salt,
      iterations: iterations,
      hash: 'SHA-512'
    },
    passwordKey,
    512 // 64 bytes (512 bits)
  );

  // Convert to hex string
  const hashHex = Array.from(new Uint8Array(derivedBits))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  // Format: iterations:salt:hash
  return `${iterations}:${saltHex}:${hashHex}`;
}

/**
 * Verify a password against a hash using Web Crypto API
 */
export async function verifyPassword(password: string, storedHash: string): Promise<boolean> {
  try {
    // Split the stored hash into its components
    const [iterations, salt, hash] = storedHash.split(':');
    const iterCount = parseInt(iterations);

    // Convert salt from hex to bytes
    const saltBytes = new Uint8Array(salt.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16)));

    // Convert password to bytes
    const encoder = new TextEncoder();
    const passwordData = encoder.encode(password);

    // Use Web Crypto API to hash the password
    const passwordKey = await crypto.subtle.importKey(
      'raw',
      passwordData,
      { name: 'PBKDF2' },
      false,
      ['deriveBits']
    );

    const derivedBits = await crypto.subtle.deriveBits(
      {
        name: 'PBKDF2',
        salt: saltBytes,
        iterations: iterCount,
        hash: 'SHA-512'
      },
      passwordKey,
      512 // 64 bytes (512 bits)
    );

    // Convert to hex string
    const derivedHash = Array.from(new Uint8Array(derivedBits))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    // Compare the hashes
    return derivedHash === hash;
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

// Export other functions from auth-utils-edge.ts
export {
  storeVerificationCode,
  verifyCode,
  createUserProfile,
  getUserByEmail,
  getUserById,
  createSession,
  getSessionByToken,
  deleteSession,
  markEmailAsVerified,
  updateUserProfile,
  updateUserEmail,
  createUser,
  updateUserPassword
} from './auth-utils-edge';
