// This file conditionally imports the correct auth-utils implementation
// based on the runtime environment (browser vs server)

// For client-side (browser environment) and Edge Runtime, use auth-utils-edge.ts
// We're using auth-utils-edge.ts for all environments to avoid Node.js crypto module issues

import {
  supabaseAdmin,
  generateRandomString,
  generateVerificationCode,
  generateSessionToken,
  sendVerificationEmail,
  storeVerification<PERSON><PERSON>,
  verifyCode,
  createUserProfile,
  getUserByEmail,
  getUserById,
  createSession,
  getSessionByToken,
  deleteSession,
  markEmailAsVerified,
  updateUserProfile,
  updateUserEmail,
  hashPassword,
  verifyPassword,
  createUser,
  updateUserPassword
} from './auth-utils-edge';

export {
  supabaseAdmin,
  generateRandomString,
  generateVerificationCode,
  generateSessionToken,
  sendVerificationEmail,
  storeVerificationCode,
  verifyCode,
  createUserProfile,
  getUserByEmail,
  getUserById,
  createSession,
  getSessionByToken,
  deleteSession,
  markEmailAsVerified,
  updateUserProfile,
  updateUser<PERSON>mail,
  hashPassword,
  verifyPassword,
  createUser,
  updateUser<PERSON>assword
};
