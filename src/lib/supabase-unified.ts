import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

// Define the URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Create singleton instances
let supabaseInstance: any = null;
let supabaseAdminInstance: any = null;
let supabaseServerInstance: any = null;

/**
 * Get the Supabase client for browser usage (singleton pattern)
 * This should be the only client used in browser contexts
 */
export function getSupabaseClient() {
  if (typeof window === 'undefined') {
    throw new Error('getSupabaseClient should only be called in browser context');
  }

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase URL or Anon Key is not set');
    throw new Error('Supabase configuration error: Missing credentials');
  }

  if (supabaseInstance) return supabaseInstance;

  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  });

  return supabaseInstance;
}

/**
 * Get the Supabase client for server usage with user context
 * This should be used in server components and API routes that need user context
 */
export function getSupabaseServerClient(sessionToken?: string) {
  if (typeof window !== 'undefined') {
    throw new Error('getSupabaseServerClient should only be called in server context');
  }

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase URL or Anon Key is not set');
    throw new Error('Supabase configuration error: Missing credentials');
  }

  // Create a new instance for each request to ensure we have the correct session
  try {
    return createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: false,
      },
      global: {
        headers: {
          Authorization: sessionToken ? `Bearer ${sessionToken}` : '',
        },
      },
    });
  } catch (error) {
    console.error('Error creating server client:', error);

    // Fallback to a client without session context
    return createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: false,
      },
    });
  }
}

/**
 * Get the Supabase admin client (singleton pattern)
 * This should only be used in server contexts that need admin privileges
 */
export function getSupabaseAdminClient() {
  if (typeof window !== 'undefined') {
    console.error('Attempted to use admin client in browser context');
    throw new Error('Admin client can only be used on the server');
  }

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Supabase URL or Service Key is not set');
    throw new Error('Database configuration error: Missing Supabase credentials');
  }

  if (supabaseAdminInstance) return supabaseAdminInstance;

  supabaseAdminInstance = createClient<Database>(
    supabaseUrl,
    supabaseServiceKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  return supabaseAdminInstance;
}

// For backward compatibility
// Use a function to lazily initialize the client only when it's actually used
// This prevents the "Multiple GoTrueClient instances" warning
// Create a lazy-loaded singleton for the browser client
// This prevents the "Multiple GoTrueClient instances" warning
let lazyLoadedClient: any = null;

export const supabase = typeof window !== 'undefined'
  ? (() => {
    if (lazyLoadedClient) return lazyLoadedClient;

    try {
      if (!supabaseUrl || !supabaseAnonKey) {
        console.error('Supabase URL or Anon Key is not set');
        throw new Error('Supabase configuration error: Missing credentials');
      }

      lazyLoadedClient = createClient<Database>(supabaseUrl, supabaseAnonKey, {
        auth: {
          persistSession: true,
          autoRefreshToken: true,
          detectSessionInUrl: true,
        },
      });

      return lazyLoadedClient;
    } catch (error) {
      console.error('Error initializing Supabase client:', error);
      // Return a dummy client that won't throw errors when methods are called
      return {
        auth: {
          getSession: async () => ({ data: { session: null }, error: null }),
          getUser: async () => ({ data: { user: null }, error: null }),
          signInWithPassword: async () => ({ data: { user: null }, error: { message: 'Client not initialized' } }),
          signUp: async () => ({ data: { user: null }, error: { message: 'Client not initialized' } }),
          signOut: async () => ({ error: null }),
        },
        from: () => ({
          select: () => ({ data: null, error: { message: 'Client not initialized' } }),
          insert: () => ({ data: null, error: { message: 'Client not initialized' } }),
          update: () => ({ data: null, error: { message: 'Client not initialized' } }),
          delete: () => ({ data: null, error: { message: 'Client not initialized' } }),
        }),
        storage: {
          from: () => ({
            upload: async () => ({ data: null, error: { message: 'Client not initialized' } }),
            getPublicUrl: () => ({ data: { publicUrl: '' } }),
            list: async () => ({ data: [], error: null }),
            remove: async () => ({ data: null, error: null }),
          }),
        },
      };
    }
  })()
  : null;

export const supabaseAdmin = typeof window === 'undefined'
  ? (() => {
    try {
      return getSupabaseAdminClient();
    } catch (error) {
      console.error('Error initializing Supabase admin client:', error);
      throw error; // Re-throw for server-side since we can't provide a dummy implementation
    }
  })()
  : null;
