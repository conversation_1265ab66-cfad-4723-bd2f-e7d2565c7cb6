/**
 * This file ensures that the Supabase client is properly initialized
 * with the correct credentials in the browser environment.
 * 
 * It should be imported in the root layout.tsx file to ensure
 * the client is initialized before any components try to use it.
 */

// This is a client-side only file
'use client';

import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

// Define the URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a singleton instance for the browser
let supabaseInstance: any = null;

// Initialize the Supabase client
export function initSupabaseClient() {
  if (typeof window === 'undefined') {
    return null; // Don't initialize on the server
  }

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase URL or Anon Key is not set');
    return null;
  }

  if (supabaseInstance) return supabaseInstance;

  try {
    console.log('Initializing Supabase client with URL:', supabaseUrl);
    supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
    });
    
    console.log('Supabase client initialized successfully');
    return supabaseInstance;
  } catch (error) {
    console.error('Error initializing Supabase client:', error);
    return null;
  }
}

// Initialize the client immediately
if (typeof window !== 'undefined') {
  initSupabaseClient();
}

export default initSupabaseClient;
