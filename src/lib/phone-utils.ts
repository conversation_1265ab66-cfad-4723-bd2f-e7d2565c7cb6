/**
 * Formats a phone number for display
 *
 * @param phoneNumber The raw phone number string (e.g., "14075551234" or "+14075551234")
 * @returns Formatted phone number string (e.g., "+****************")
 */
export function formatPhoneNumber(phoneNumber: string | null | undefined): string {
  if (!phoneNumber) return '';

  // If the phone number already contains spaces or formatting, return it as is
  if (phoneNumber.includes(' ') || phoneNumber.includes('-') || phoneNumber.includes('(')) {
    return phoneNumber;
  }

  // Remove any non-digit characters except the leading +
  let cleaned = phoneNumber.replace(/[^\d+]/g, '');

  // Ensure the number has a + prefix if it doesn't already
  if (!cleaned.startsWith('+')) {
    // If it starts with a country code like 1, add the +
    cleaned = '+' + cleaned;
  }

  // Extract the country code and national number
  const match = cleaned.match(/^\+(\d{1,3})(\d+)$/);
  if (!match) return phoneNumber; // Return original if no match

  const [, countryCode, nationalNumber] = match;

  // Format based on country code
  if (countryCode === '1') {
    // US/Canada: +1 (XXX) XXX-XXXX
    if (nationalNumber.length === 10) {
      return `+1 (${nationalNumber.slice(0, 3)}) ${nationalNumber.slice(3, 6)}-${nationalNumber.slice(6)}`;
    }
  }

  // Default international format: +XX XXXX XXXX
  return `+${countryCode} ${nationalNumber.replace(/(\d{4})(?=\d)/g, '$1 ').trim()}`;
}
