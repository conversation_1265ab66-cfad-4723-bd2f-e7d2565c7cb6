import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Define the URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a Supabase client for browser usage
export const createBrowserClient = () => {
  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  });
};

// Create a Supabase client for server usage
export const createServerClient = async () => {
  const cookieStore = await cookies();
  const sessionToken = cookieStore.get('session_token')?.value;

  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false,
    },
    global: {
      headers: {
        Authorization: sessionToken ? `Bearer ${sessionToken}` : '',
      },
    },
  });
};

// For backward compatibility and browser usage
export const supabase = createBrowserClient();
