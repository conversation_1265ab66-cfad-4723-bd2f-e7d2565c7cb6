import { supabaseAdmin } from '@/lib/supabase-unified';
import { User } from '@supabase/supabase-js';

/**
 * Ensures that a mapping exists between custom_users and auth.users
 * If no mapping exists, it will create one
 *
 * @param customUserId The ID of the user in the custom_users table
 * @returns The auth user ID from the mapping
 */
export async function ensureAuthUserMapping(customUserId: string): Promise<string | null> {
  try {
    console.log(`[ensureAuthUserMapping] Starting for custom user ID: ${customUserId}`);

    // First, try to get the existing mapping
    const { data: existingMapping, error: mappingError } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('auth_user_id')
      .eq('custom_user_id', customUserId)
      .maybeSingle();

    // If we already have a mapping, return the auth user ID
    if (existingMapping && existingMapping.auth_user_id) {
      console.log(`[ensureAuthUserMapping] Found existing mapping: ${existingMapping.auth_user_id}`);
      return existingMapping.auth_user_id;
    }

    // If there was an error other than "no rows returned", log it
    if (mappingError && mappingError.code !== 'PGRST116') {
      console.error('[ensureAuthUserMapping] Error checking auth user mapping:', mappingError);
      return customUserId; // Fallback to using the custom user ID directly
    }

    // No mapping exists, so we need to create one
    // First, get the user's email and name from custom_users
    const { data: userData, error: userError } = await supabaseAdmin
      .from('custom_users')
      .select('email, first_name, last_name')
      .eq('id', customUserId)
      .single();

    if (userError || !userData) {
      console.error('[ensureAuthUserMapping] Error getting user data:', userError);
      return customUserId; // Fallback to using the custom user ID directly
    }

    console.log(`[ensureAuthUserMapping] Found user data for ${customUserId}: ${userData.email}`);

    // Now find the corresponding auth.users record by email
    // Note: auth.users is a special table in Supabase that requires a different query approach
    // Try to get the user directly by email instead of listing all users
    let authUser: User | undefined;
    try {
      const { data, error } = await supabaseAdmin.auth.admin.getUserByEmail(userData.email);
      if (error) {
        console.error('[ensureAuthUserMapping] Error getting auth user by email:', error);
        // Continue with the process, we'll create a new user if needed
      } else if (data && data.user) {
        authUser = data.user;
        console.log(`[ensureAuthUserMapping] Found auth user: ${authUser.id}`);
      }
    } catch (error) {
      console.error('[ensureAuthUserMapping] Exception getting auth user by email:', error);
      // Continue with the process, we'll create a new user if needed
    }

    // If no auth user exists with this email, we need to create one
    if (!authUser || !authUser.id) {
      console.log(`[ensureAuthUserMapping] No auth user found with email: ${userData.email}, Creating one...`);

      try {
        // Create a new auth user with the same email
        const { data: newAuthUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
          email: userData.email,
          email_confirm: true,
          user_metadata: {
            first_name: userData.first_name || '',
            last_name: userData.last_name || ''
          }
        });

        if (createError) {
          console.error('[ensureAuthUserMapping] Error creating auth user:', createError);
          return customUserId; // Fallback to using the custom user ID directly
        }

        if (!newAuthUser || !newAuthUser.user || !newAuthUser.user.id) {
          console.error('[ensureAuthUserMapping] Failed to create auth user');
          return customUserId; // Fallback to using the custom user ID directly
        }

        // Use the newly created auth user
        authUser = newAuthUser.user;
        console.log(`[ensureAuthUserMapping] Created new auth user: ${authUser.id}`);
      } catch (error) {
        console.error('[ensureAuthUserMapping] Exception creating auth user:', error);
        return customUserId; // Fallback to using the custom user ID directly
      }
    }

    // Check if a mapping already exists for this auth user to prevent duplicates
    const { data: existingAuthMapping } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('custom_user_id')
      .eq('auth_user_id', authUser.id)
      .maybeSingle();

    if (existingAuthMapping) {
      console.log(`[ensureAuthUserMapping] Mapping already exists for auth user: ${authUser.id}`);
      return authUser.id;
    }

    // Create the mapping
    try {
      const { data: newMapping, error: createError } = await supabaseAdmin
        .from('auth_users_mapping')
        .insert({
          custom_user_id: customUserId,
          auth_user_id: authUser.id
        })
        .select('auth_user_id')
        .single();

      if (createError) {
        console.error('[ensureAuthUserMapping] Error creating auth user mapping:', createError);
        return authUser.id; // Return the auth user ID even if mapping creation fails
      }

      console.log(`[ensureAuthUserMapping] Successfully created auth user mapping for user: ${customUserId} with auth user: ${authUser.id}`);
      return newMapping.auth_user_id;
    } catch (error) {
      console.error('[ensureAuthUserMapping] Exception creating auth user mapping:', error);
      // If there's an error, but we have the auth user ID, return it anyway as a fallback
      return authUser.id;
    }
  } catch (error) {
    console.error('[ensureAuthUserMapping] Exception in ensureAuthUserMapping:', error);
    return customUserId; // Fallback to using the custom user ID directly
  }
}
