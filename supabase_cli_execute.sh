#!/bin/bash

# Install Supabase CLI if not already installed
if ! command -v supabase &> /dev/null; then
    echo "Installing Supabase CLI..."
    npm install -g supabase
fi

# Login to Supabase (this will open a browser window)
echo "Logging in to Supabase..."
supabase login

# Link to the project
echo "Linking to Supabase project..."
supabase link --project-ref ccwvtcudztphwwzzgwvg

# Create a migration file from our SQL
echo "Creating migration file..."
MIGRATION_FILE="supabase/migrations/$(date +%Y%m%d%H%M%S)_fix_trustees.sql"
cp fix_remote_db.sql "$MIGRATION_FILE"

# Push the migration
echo "Pushing migration to remote database..."
supabase db push

echo "Migration complete!"
