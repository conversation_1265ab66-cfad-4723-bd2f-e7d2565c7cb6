#!/bin/bash

# Exit on error
set -e

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check if Supabase CLI is installed
if ! command_exists supabase; then
  echo "Error: Supabase CLI is not installed."
  echo "Please install it by following the instructions at: https://supabase.com/docs/guides/cli"
  exit 1
fi

echo "=== Supabase Deployment Script ==="
echo "This script will deploy all changes to your Supabase project."
echo ""

# Step 1: Deploy database changes
echo "Step 1: Deploying database changes..."

# Check if we're already logged in
echo "Checking Supabase login status..."
if ! supabase projects list &>/dev/null; then
  echo "Not logged in. Please log in to Supabase..."
  supabase login
else
  echo "Already logged in to Supabase."
fi

# Link to the project
echo "Linking to Supabase project..."
if ! supabase projects list | grep -q "ccwvtcudztphwwzzgwvg"; then
  echo "Linking to project ccwvtcudztphwwzzgwvg..."
  supabase link --project-ref ccwvtcudztphwwzzgwvg
else
  echo "Already linked to project."
fi

# Push the database changes
echo "Pushing database migrations..."
supabase db push --include-all

# Step 2: Deploy Edge Functions
echo ""
echo "Step 2: Deploying Edge Functions..."

# Deploy the _shared directory if it exists
if [ -d "supabase/functions/_shared" ]; then
  echo "Deploying _shared directory..."
  supabase functions deploy _shared
fi

# Deploy the send-trustee-invitation function
echo "Deploying send-trustee-invitation function..."
supabase functions deploy send-trustee-invitation

# Step 3: Set environment variables
echo ""
echo "Step 3: Setting environment variables..."

# Check if .env.local exists
if [ -f ".env.local" ]; then
  echo "Setting environment variables from .env.local..."
  supabase secrets set --env-file .env.local
else
  echo "Warning: .env.local file not found. Skipping environment variable setup."
fi

echo ""
echo "=== Deployment Complete ==="
echo "All changes have been deployed to your Supabase project."
