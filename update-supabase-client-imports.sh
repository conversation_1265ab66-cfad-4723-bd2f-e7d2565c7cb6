#!/bin/bash

# Find all files that import from @/integrations/supabase/client and update them to use supabase-unified
find src -type f -name "*.ts" -o -name "*.tsx" | xargs grep -l "from '@/integrations/supabase/client'" | xargs sed -i '' "s/import { supabase } from '@\/integrations\/supabase\/client';/import { supabase } from '@\/lib\/supabase-unified';/g"

echo "Updated all files to use supabase-unified"
