#!/bin/bash

# This script deploys the send-trustee-invitation Edge Function to Supabase

# Set variables
PROJECT_ID="ccwvtcudztphwwzzgwvg"
FUNCTION_NAME="send-trustee-invitation"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
  echo "Supabase CLI is not installed. Please install it first."
  exit 1
fi

# Deploy the function
echo "Deploying $FUNCTION_NAME Edge Function..."
supabase functions deploy $FUNCTION_NAME --project-ref $PROJECT_ID

echo "Deployment complete!"
