/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['ccwvtcudztphwwzzgwvg.supabase.co'],
  },
  // Server Actions are available by default in Next.js 14

  // Fix for browser extension attributes warning
  compiler: {
    styledComponents: true,
  },
  // Webpack configuration
  webpack: (config, { isServer }) => {
    // Only apply babel-loader on client-side
    if (!isServer) {
      config.module.rules.push({
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['next/babel'],
          },
        },
      });
    }
    return config;
  },
  // Keep trailing slashes for consistent URLs
  trailingSlash: true,
  // Disable CSR bailout warnings
  experimental: {
    missingSuspenseWithCSRBailout: false
  },
  // Configure external packages for server components
  serverComponentsExternalPackages: ['crypto'],
};

export default nextConfig;
