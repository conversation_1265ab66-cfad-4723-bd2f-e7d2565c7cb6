#!/bin/bash

# Set Supabase secrets for Edge Functions
echo "Setting Supabase secrets for Edge Functions..."

# Set the RESEND_API_KEY
echo "Setting RESEND_API_KEY..."
supabase secrets set RESEND_API_KEY=re_b3UB5Mhg_PTyMeZ7YSXtNepXb83sasTHP

# Set the PUBLIC_SITE_URL
echo "Setting PUBLIC_SITE_URL..."
supabase secrets set PUBLIC_SITE_URL=https://legalock.com

# Set the NEXT_PUBLIC_SITE_URL
echo "Setting NEXT_PUBLIC_SITE_URL..."
supabase secrets set NEXT_PUBLIC_SITE_URL=https://legalock.com

echo "Secrets set successfully!"
