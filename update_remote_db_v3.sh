#!/bin/bash

# Start the MCP server if it's not already running
echo "Starting MCP server..."
npx @supabase/mcp-server-supabase@latest --access-token ******************************************** &
MCP_PID=$!

# Wait for the server to start
sleep 5

# Execute SQL script on remote database using curl
echo "Updating remote database schema..."
curl -X POST http://localhost:54321/sql -H "Content-Type: application/json" -d @- << EOF
{
  "query": "$(cat fix_remote_db.sql | tr '\n' ' ')"
}
EOF

# Kill the MCP server
kill $MCP_PID

echo "Remote database update complete!"
