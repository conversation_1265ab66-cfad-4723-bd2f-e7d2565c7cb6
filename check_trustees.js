const { execSync } = require('child_process');

// Execute SQL query using MCP
function executeQuery(query) {
  try {
    const result = execSync(`echo "${query}" | npx @supabase/mcp-server-supabase@latest sql`, { 
      encoding: 'utf-8',
      stdio: ['pipe', 'pipe', 'pipe']
    });
    return result;
  } catch (error) {
    console.error('Error executing query:', error.message);
    if (error.stdout) console.log('Output:', error.stdout);
    if (error.stderr) console.log('Error output:', error.stderr);
    return null;
  }
}

// Check trustees table schema
console.log('Checking trustees table schema...');
const trusteesSchema = executeQuery(`
  SELECT table_name, column_name, data_type 
  FROM information_schema.columns 
  WHERE table_schema = 'public' AND table_name = 'trustees' 
  ORDER BY table_name, ordinal_position;
`);
console.log('Trustees table schema:', trusteesSchema);

// Check foreign key constraints on trustees table
console.log('Checking foreign key constraints on trustees table...');
const trusteesFKs = executeQuery(`
  SELECT tc.constraint_name, tc.table_name, kcu.column_name, 
         ccu.table_name AS foreign_table_name, ccu.column_name AS foreign_column_name 
  FROM information_schema.table_constraints AS tc 
  JOIN information_schema.key_column_usage AS kcu 
    ON tc.constraint_name = kcu.constraint_name AND tc.table_schema = kcu.table_schema 
  JOIN information_schema.constraint_column_usage AS ccu 
    ON ccu.constraint_name = tc.constraint_name AND ccu.table_schema = tc.table_schema 
  WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = 'trustees';
`);
console.log('Trustees foreign keys:', trusteesFKs);

// Check if auth.users table exists
console.log('Checking if auth.users table exists...');
const authUsersExists = executeQuery(`
  SELECT EXISTS (
    SELECT FROM pg_tables 
    WHERE schemaname = 'auth' AND tablename = 'users'
  ) AS auth_users_exists;
`);
console.log('Auth users exists:', authUsersExists);

// Check if custom_users table exists
console.log('Checking if custom_users table exists...');
const customUsersExists = executeQuery(`
  SELECT EXISTS (
    SELECT FROM pg_tables 
    WHERE schemaname = 'public' AND tablename = 'custom_users'
  ) AS custom_users_exists;
`);
console.log('Custom users exists:', customUsersExists);
