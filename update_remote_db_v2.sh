#!/bin/bash

# Create a temporary directory for the Supabase project
TEMP_DIR=$(mktemp -d)
cd $TEMP_DIR

# Initialize a Supabase project
echo "Initializing Supabase project..."
supabase init

# Create a migration file
mkdir -p supabase/migrations
cp /Users/<USER>/Desktop/Legalock/fix_remote_db.sql supabase/migrations/20240601000000_fix_trustees.sql

# Link to the remote project
echo "Linking to remote project..."
supabase link --project-ref ccwvtcudztphbwkqnvxs

# Push the migration to the remote database
echo "Pushing migration to remote database..."
supabase db push

# Clean up
cd /Users/<USER>/Desktop/Legalock
rm -rf $TEMP_DIR

echo "Remote database update complete!"
