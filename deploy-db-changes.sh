#!/bin/bash

# Exit on error
set -e

echo "Deploying database changes..."

# Login to Supabase (this will open a browser window)
echo "Logging in to Supabase..."
supabase login

# Link to the project
echo "Linking to Supabase project..."
supabase link --project-ref ccwvtcudztphwwzzgwvg

# Push the updated migrations
echo "Pushing database migrations..."
supabase db push

echo "Database changes deployed successfully!"
