#!/bin/bash

# Script to pull current remote Supabase configuration using MCP
echo "Pulling current remote Supabase configuration using MCP..."

# Create a backup of the current config if it exists
if [ -f "supabase/config.toml" ]; then
  echo "Creating backup of current config..."
  cp supabase/config.toml supabase/config.toml.backup.$(date +%Y%m%d%H%M%S)
fi

# Use MCP to pull the current configuration
echo "Starting MCP server..."
npx @supabase/mcp-server-supabase@latest --access-token ******************************************** start &
MCP_PID=$!

# Wait for the server to start
echo "Waiting for MCP server to start..."
sleep 5

# Query the configuration
echo "Querying project configuration..."
mkdir -p supabase
curl -s http://localhost:54321/project > supabase/project_info.json

# Extract the configuration and save it to config.toml
echo "Extracting configuration..."
cat supabase/project_info.json | jq -r '.config' > supabase/config.toml

# Kill the MCP server
echo "Stopping MCP server..."
kill $MCP_PID

echo "Configuration pulled successfully."
echo "Current remote configuration:"
cat supabase/config.toml

echo "Done."
