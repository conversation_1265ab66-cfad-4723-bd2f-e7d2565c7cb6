#!/bin/bash

# <PERSON>ript to pull current remote Supabase configuration using Supabase CLI
echo "Pulling current remote Supabase configuration using Supabase CLI..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI not found. Installing..."
    npm install -g supabase
fi

# Create a backup of the current config if it exists
if [ -f "supabase/config.toml" ]; then
  echo "Creating backup of current config..."
  cp supabase/config.toml supabase/config.toml.backup.$(date +%Y%m%d%H%M%S)
fi

# Login to Supabase
echo "Logging in to Supabase..."
supabase login --token ********************************************

# Link to the project
echo "Linking to Supabase project..."
supabase link --project-ref ccwvtcudztphwwzzgwvg

# Pull the configuration
echo "Pulling configuration..."
supabase pull

echo "Configuration pulled successfully."
echo "Current remote configuration:"
cat supabase/config.toml

echo "Done."
