const fs = require('fs');
const { execSync } = require('child_process');

// Read the SQL file
const sql = fs.readFileSync('fix_remote_db.sql', 'utf8');

// Split the SQL into individual statements
const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

// Execute each statement using MCP
console.log(`Executing ${statements.length} SQL statements...`);

for (let i = 0; i < statements.length; i++) {
  const stmt = statements[i].trim();
  console.log(`Executing statement ${i + 1}/${statements.length}...`);
  
  try {
    // Execute the statement using MCP
    execSync(`echo "${stmt};" | npx @supabase/mcp-server-supabase@latest sql`, { stdio: 'inherit' });
  } catch (error) {
    console.error(`Error executing statement ${i + 1}:`, error.message);
  }
}

console.log('Remote database update complete!');
