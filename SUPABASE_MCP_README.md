# Supabase MCP (Management Console Project) Setup for VS Code

This document explains how to use Supabase MCP with VS Code for the Legalock project.

## What is MCP?

MCP (Management Console Project) is a tool that allows you to interact with your Supabase project directly from your local development environment. It provides a way to execute SQL queries, manage database schema, and interact with your Supabase project without having to use the Supabase dashboard.

## Configuration

The MCP has been configured with your personal access token:

```
********************************************
```

This token is stored in the following locations:
- `.vscode/settings.json` - For VS Code integration
- `.cursor/mcp.json` - For Cursor editor integration

## Using MCP in VS Code

### VS Code Tasks

Several VS Code tasks have been set up to make it easier to work with MCP:

1. **Start Supabase MCP Server**
   - Starts the MCP server in a terminal window
   - Use this when you need to keep the server running for multiple operations

2. **Test Supabase MCP Connection**
   - Runs a simple query to test the connection to your Supabase project
   - Good for verifying that your token is working

3. **Execute SQL with <PERSON>P**
   - Prompts you for a SQL query and executes it against your Supabase project
   - Useful for quick one-off queries

4. **Execute SQL File with MCP**
   - Prompts you for a SQL file path and executes the contents against your Supabase project
   - Good for running larger scripts or migrations

To run these tasks:
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Tasks: Run Task"
3. Select the task you want to run

### Direct Terminal Usage

You can also run MCP commands directly in the terminal:

```bash
# Run a simple query
echo "SELECT NOW();" | npx @supabase/mcp-server-supabase@latest --access-token ********************************************

# Run a query from a file
cat query.sql | npx @supabase/mcp-server-supabase@latest --access-token ********************************************

# Start the MCP server (interactive mode)
npx @supabase/mcp-server-supabase@latest --access-token ********************************************
```

## Common MCP Operations

### Viewing Tables

```sql
-- List all tables
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Describe a table
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'your_table_name';
```

### Managing Data

```sql
-- Insert data
INSERT INTO your_table_name (column1, column2) VALUES ('value1', 'value2');

-- Update data
UPDATE your_table_name SET column1 = 'new_value' WHERE id = 'some_id';

-- Delete data
DELETE FROM your_table_name WHERE id = 'some_id';
```

### Managing Schema

```sql
-- Create a new table
CREATE TABLE new_table (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add a column
ALTER TABLE your_table_name ADD COLUMN new_column TEXT;

-- Create an index
CREATE INDEX idx_your_table_column ON your_table_name(column_name);
```

## Troubleshooting

If you encounter issues with MCP:

1. **Connection Problems**
   - Verify your access token is correct
   - Check your internet connection
   - Ensure your Supabase project is active

2. **Permission Errors**
   - Your access token might not have the necessary permissions
   - Check the role associated with your token

3. **SQL Errors**
   - Check your SQL syntax
   - Verify table and column names
   - Look for typos in your queries

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase CLI Documentation](https://supabase.com/docs/reference/cli)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
