project_id = "ccwvtcudztphwwzzgwvg"

[storage]
enabled = true
file_size_limit = "50MiB"

[auth]
enabled = true
site_url = "https://www.legalock.com"
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10
additional_redirect_urls = [
  "https://www.legalock.com/login",
  "https://www.legalock.com/callback",
  "https://legalock.com/login", 
  "https://legalock.com/callback",
  "http://localhost:3000"
]

[auth.mfa]
max_enrolled_factors = 3
[auth.mfa.totp]
enroll_enabled = true
verify_enabled = true
[auth.mfa.phone]
enroll_enabled = false
verify_enabled = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true
secure_password_change = true
max_frequency = "1m0s"
otp_length = 6
otp_expiry = 86400

# SMTP Configuration
[auth.email.smtp]
admin_email = "<EMAIL>"
host = "smtp.resend.com"
port = "465"
user = "resend"
pass = "6beb8443f0866571e40c9e22e0e81c9e9a47d0a595b1cf12af00be5f60cd3ea9"
sender_name = "Legalock"

# External OAuth Providers
[auth.external.google]
enabled = true
client_id = "354396090693-amoa24gt5flqk4fvpnnfatqumcblpgpa.apps.googleusercontent.com"
secret = "bcac794b0cac5f66ed09aea5ab9adc70f507bff138b265056928278fd4a534b6"

# Database Functions section removed for now

# Edge Functions configuration
[functions.check-subscription]
verify_jwt = true

[functions.create-checkout]
verify_jwt = true

[functions.deliver-time-capsule]
verify_jwt = false

[functions.send-trustee-invitation]
verify_jwt = true
import_map = "./functions/send-trustee-invitation/deno.json"
entrypoint = "./functions/send-trustee-invitation/index.ts"

[functions.estate-recommendations]
verify_jwt = true

[functions.send-email-hook]
enabled = true
verify_jwt = true
import_map = "./functions/send-email-hook/deno.json"
entrypoint = "./functions/send-email-hook/index.ts"
