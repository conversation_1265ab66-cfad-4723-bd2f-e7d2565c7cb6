# Supabase SQL Functions

This directory contains SQL functions that need to be deployed to your Supabase project.

## Deploying the SQL Functions

You can deploy these SQL functions to your Supabase project using the Supabase CLI or by running them directly in the SQL editor in the Supabase dashboard.

### Option 1: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Create a new query
4. Copy and paste the contents of each SQL file in this directory
5. Run the query

### Option 2: Using the Supabase CLI

1. Install the Supabase CLI if you haven't already:
   ```
   npm install -g supabase
   ```

2. Log in to your Supabase account:
   ```
   supabase login
   ```

3. Link your project:
   ```
   supabase link --project-ref YOUR_PROJECT_REF
   ```

4. Deploy the SQL functions:
   ```
   supabase db push
   ```

## SQL Functions

### `activate_trustee.sql`

This function activates a trustee by updating their status to 'active' and setting the trustee_user_id.

Usage:
```sql
SELECT activate_trustee('trustee_id_here', 'user_id_here');
```

### `direct_update_trustee.sql`

This function directly updates a trustee record using a SQL UPDATE statement.

Usage:
```sql
SELECT direct_update_trustee('trustee_id_here', 'user_id_here', NOW());
```

## Troubleshooting

If you encounter any issues with these functions:

1. Check the Supabase logs for any error messages
2. Verify that the functions have been created by querying the `pg_proc` system catalog:
   ```sql
   SELECT proname FROM pg_proc WHERE proname IN ('activate_trustee', 'direct_update_trustee');
   ```
3. If the functions exist but are not working as expected, try dropping and recreating them:
   ```sql
   DROP FUNCTION IF EXISTS activate_trustee;
   DROP FUNCTION IF EXISTS direct_update_trustee;
   ```
   Then redeploy the functions.

## Manual SQL Update

If all else fails, you can manually update the trustee record using SQL:

```sql
UPDATE trustees
SET 
  trustee_user_id = 'user_id_here',
  status = 'active',
  invitation_accepted_at = NOW()
WHERE id = 'trustee_id_here';
```
