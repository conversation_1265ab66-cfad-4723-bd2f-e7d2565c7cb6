
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Max-Age": "86400",
};

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

interface TrusteeInvitationRequest {
  inviterName: string;
  inviterEmail: string;
  trusteeName: string;
  trusteeEmail: string;
  permissions: string[];
  inviteId: string;
  message?: string;
  magicLink?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// No longer using token generation

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    let {
      inviterName,
      inviterEmail,
      trusteeName,
      trusteeEmail,
      permissions,
      inviteId,
      message,
      magicLink
    }: TrusteeInvitationRequest = await req.json();

    // Generate a secure invitation link if not provided
    if (!magicLink) {
      // Create an invitation link with trustee ID and email
      const appUrl = Deno.env.get("PUBLIC_SITE_URL") || Deno.env.get("NEXT_PUBLIC_SITE_URL") || "https://legalock.com";

      // Generate a secure token for additional verification
      const tokenBytes = new Uint8Array(32);
      crypto.getRandomValues(tokenBytes);
      const secureToken = Array.from(tokenBytes)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      // Store the token in the database for verification
      try {
        const { error: tokenError } = await supabase
          .from('trustee_tokens')
          .insert({
            trustee_id: inviteId,
            token: secureToken,
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days expiry
          });

        if (tokenError) {
          console.error("Error storing trustee token:", tokenError);
          // Continue anyway, as we can still use the basic link
        }
      } catch (tokenStoreError) {
        console.error("Exception storing trustee token:", tokenStoreError);
        // Continue anyway, as we can still use the basic link
      }

      // Include the token in the link for additional security
      magicLink = `${appUrl}/trustee/accept?id=${inviteId}&email=${encodeURIComponent(trusteeEmail)}&token=${secureToken}`;
      console.log("Generated secure invitation link with token");
    }

    // Ensure names are properly capitalized and handle missing values
    inviterName = inviterName ? inviterName.split(' ').map(name =>
      name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
    ).join(' ') : 'A Legalock User';

    trusteeName = trusteeName ? trusteeName.split(' ').map(name =>
      name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()
    ).join(' ') : 'Trustee';

    // Ensure we have valid email addresses and invitation ID
    if (!trusteeEmail || !inviteId) {
      return new Response(
        JSON.stringify({ error: "Trustee email and invitation ID are required" }),
        { status: 400, headers: { "Content-Type": "application/json", ...corsHeaders } }
      );
    }

    // Use a default inviter email if none is provided
    inviterEmail = inviterEmail || '<EMAIL>';

    console.log(`Sending invitation to ${trusteeEmail} from ${inviterEmail}`);

    // Format permissions as a readable list with descriptions
    const permissionDescriptions = {
      assets: "Access to financial and physical assets inventory",
      vault: "Access to important documents and files",
      contacts: "Access to emergency contacts list",
      services: "Access to digital services that need to be managed",
      'last-wishes': "Access to final wishes and arrangements"
    };

    const permissionsList = permissions.map(p => {
      const description = permissionDescriptions[p as keyof typeof permissionDescriptions] || p;
      return `<li><strong>${p.charAt(0).toUpperCase() + p.slice(1)}</strong>: ${description}</li>`;
    }).join('');

    const emailResponse = await resend.emails.send({
      from: "Legalock <<EMAIL>>",
      to: [trusteeEmail],
      subject: `${inviterName} has invited you to be their trustee on Legalock`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #4f46e5; margin-bottom: 10px;">Legalock Trustee Invitation</h1>
            <p style="color: #6b7280; font-size: 16px;">Secure Digital Legacy Management</p>
          </div>

          <p style="font-size: 16px;">Hello ${trusteeName},</p>

          <p style="font-size: 16px;"><strong>${inviterName}</strong> (${inviterEmail}) has invited you to be their trustee on Legalock.</p>

          ${message ? `<div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #3b82f6;">
            <p style="font-size: 16px; margin-top: 0;"><strong>Personal message from ${inviterName}:</strong></p>
            <p style="font-size: 15px; font-style: italic;">"${message}"</p>
          </div>` : ''}

          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="font-size: 16px; margin-top: 0;"><strong>What is a Trustee?</strong></p>
            <p style="font-size: 15px;">A trustee is someone trusted to access and manage important digital assets and documents in case of emergency or after the person's passing. As a trustee, you'll help ensure their digital legacy is properly handled according to their wishes.</p>
          </div>

          <p style="font-size: 16px;"><strong>You will be granted access to the following:</strong></p>
          <ul style="background-color: #f9fafb; padding: 15px 15px 15px 35px; border-radius: 6px;">
            ${permissionsList}
          </ul>

          <p style="font-size: 15px;"><em>Note: You will only gain access to these items after verification of the emergency situation or passing of ${inviterName}.</em></p>

          <div style="background-color: #fff8e6; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <p style="font-size: 16px; margin-top: 0;"><strong>Important Instructions:</strong></p>
            <ol style="font-size: 15px; margin-bottom: 0; padding-left: 20px;">
              <li>Click the "Accept Invitation" button below</li>
              <li>Review the trustee responsibilities and check the acceptance box</li>
              <li>If you don't have a Legalock account, you'll need to create one</li>
              <li>If you already have an account, simply sign in</li>
              <li>Your trustee status will be automatically activated</li>
            </ol>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${magicLink}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block; font-size: 16px;">Accept Invitation</a>
            <p style="font-size: 14px; color: #6b7280; margin-top: 10px;">Or copy and paste this link in your browser: <br><span style="color: #4f46e5;">${magicLink}</span></p>
          </div>

          <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 20px;">
            <p style="font-size: 15px;">If you have any questions about this responsibility, please contact ${inviterName} directly at ${inviterEmail}.</p>
            <p style="font-size: 15px;">Thank you for being a trusted person in ${inviterName}'s life.</p>
            <p style="font-size: 15px; margin-bottom: 0;">Sincerely,<br>The Legalock Team</p>
          </div>
        </div>
      `,
    });

    console.log("Email sent successfully:", emailResponse);

    return new Response(JSON.stringify(emailResponse), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-trustee-invitation function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
