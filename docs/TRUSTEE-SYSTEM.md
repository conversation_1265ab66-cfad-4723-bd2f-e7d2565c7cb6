# Trustee System Documentation

This document provides an overview of the trustee system in Legalock, including its architecture, components, and how it works.

## Overview

The trustee system allows users to designate trusted individuals who will have access to their digital legacy after their passing. Trustees can be invited, and once they accept the invitation, they become linked to the user's account.

## Components

### Database Tables

1. **trustees**: Stores trustee information
   - `id`: UUID primary key
   - `user_id`: UUID of the grantor (the user who created the trustee)
   - `trustee_email`: Email of the invited trustee
   - `trustee_user_id`: User ID of the trustee (once they accept)
   - `first_name`, `last_name`: Trustee's name
   - `status`: 'pending', 'active', or 'revoked'
   - `permissions`: Array of permissions (assets, vault, contacts, etc.)
   - `invitation_sent_at`, `invitation_accepted_at`: Timestamps
   - `inviter_email`, `inviter_first_name`, `inviter_last_name`: Inviter information

2. **trustee_tokens**: Stores secure tokens for invitation verification
   - `id`: UUID primary key
   - `trustee_id`: Reference to the trustee record
   - `token`: Secure token for verification
   - `expires_at`: Token expiration timestamp

3. **trustee_operations_log**: Audit log for trustee operations
   - `id`: UUID primary key
   - `operation`: Type of operation (INVITE, ACTIVATE, etc.)
   - `trustee_id`: Reference to the trustee record
   - `performed_by`: User ID who performed the operation
   - `details`: JSONB with additional details
   - `created_at`: Timestamp

### SQL Functions

1. **activate_trustee**: Activates a trustee by updating their status and setting the trustee_user_id
   ```sql
   activate_trustee(p_trustee_id UUID, p_user_id UUID)
   ```

2. **direct_update_trustee_invitation**: Updates a trustee invitation record
   ```sql
   direct_update_trustee_invitation(p_trustee_id UUID, p_current_time TIMESTAMP WITH TIME ZONE)
   ```

### API Endpoints

1. **/api/trustees/activate**: Unified endpoint for activating trustees
2. **/api/send-trustee-invitation**: Endpoint for sending trustee invitations

### Edge Functions

1. **send-trustee-invitation**: Sends invitation emails to trustees

## Invitation Flow

1. User adds a trustee through the UI
2. System creates a trustee record with status 'pending'
3. System generates a secure token and stores it in trustee_tokens
4. System sends an invitation email with a secure link
5. Trustee clicks the link and is directed to the acceptance page
6. Trustee accepts the invitation and is linked to the trustee record
7. System updates the trustee record with status 'active' and sets trustee_user_id

## Access Flow

1. When a user passes away, a death notification is created
2. Once the death is verified, trustees gain access to the user's data
3. Trustees can access the data based on their permissions
4. All access is logged for audit purposes

## Troubleshooting

If issues occur with the trustee system:

1. Check the trustee_operations_log table for errors
2. Verify that the trustee record exists and has the correct status
3. Check that the trustee_user_id is set correctly
4. Verify that the email addresses match

## Maintenance

The trustee system has been consolidated and simplified. The following components are used:

- `activate_trustee` SQL function for activation
- `direct_update_trustee_invitation` SQL function for invitation updates
- `/api/trustees/activate` API endpoint for activation
- `/api/send-trustee-invitation` API endpoint for sending invitations
- `send-trustee-invitation` Edge Function for sending emails

All other components have been removed to simplify maintenance.
