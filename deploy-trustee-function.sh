#!/bin/bash

# Deploy the send-trustee-invitation Edge Function using the Supabase Management API
echo "Deploying send-trustee-invitation Edge Function..."

# Set variables
PROJECT_ID="ccwvtcudztphwwzzgwvg"
FUNCTION_NAME="send-trustee-invitation"
SUPABASE_ACCESS_TOKEN="********************************************"

# Create a temporary config file
cat > temp_config.toml << EOL
project_id = "${PROJECT_ID}"

[functions.${FUNCTION_NAME}]
verify_jwt = true
EOL

# Create a temporary directory for the function
mkdir -p temp_function/${FUNCTION_NAME}
cp supabase/functions/${FUNCTION_NAME}/index.ts temp_function/${FUNCTION_NAME}/

# Deploy the function using the temporary config
echo "Deploying ${FUNCTION_NAME} function..."
cd temp_function && supabase functions deploy ${FUNCTION_NAME} --project-ref ${PROJECT_ID} --config-file ../temp_config.toml

# Clean up
cd ..
rm -rf temp_function
rm temp_config.toml

# Load environment variables from .env.local
if [ -f .env.local ]; then
  echo "Loading environment variables from .env.local..."
  export $(grep -v '^#' .env.local | xargs)
fi

# Set the environment variables
echo "Setting environment variables..."
curl -X PATCH \
  "https://api.supabase.com/v1/projects/${PROJECT_ID}/functions/${FUNCTION_NAME}/edge-config" \
  -H "Authorization: Bearer ${SUPABASE_ACCESS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "RESEND_API_KEY": "'"${RESEND_API_KEY}"'",
    "SUPABASE_URL": "'"${NEXT_PUBLIC_SUPABASE_URL}"'",
    "SUPABASE_SERVICE_ROLE_KEY": "'"${SUPABASE_SERVICE_ROLE_KEY}"'",
    "PUBLIC_SITE_URL": "'"${NEXT_PUBLIC_SITE_URL:-https://www.legalock.com}"'"
  }'

echo "Deployment complete!"
