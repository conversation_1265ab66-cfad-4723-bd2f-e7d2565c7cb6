const fs = require('fs');
const { spawn, execSync } = require('child_process');
const path = require('path');

// Read the SQL file
const sqlFilePath = path.join(__dirname, 'fix_remote_db.sql');
const sql = fs.readFileSync(sqlFilePath, 'utf8');

// Split the SQL into individual statements
const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

// Function to execute a single SQL statement
async function executeStatement(statement) {
  return new Promise((resolve, reject) => {
    // Create a temporary file with the SQL statement
    const tempFile = path.join(__dirname, 'temp_statement.sql');
    fs.writeFileSync(tempFile, statement + ';');
    
    try {
      // Execute the statement using MCP
      console.log(`Executing statement: ${statement.substring(0, 50)}...`);
      
      // Use the MCP server to execute the SQL
      const result = execSync(`npx @supabase/mcp-server-supabase@latest --access-token ******************************************** < ${tempFile}`, {
        stdio: 'inherit'
      });
      
      resolve(result);
    } catch (error) {
      console.error('Error executing statement:', error.message);
      reject(error);
    } finally {
      // Clean up the temporary file
      fs.unlinkSync(tempFile);
    }
  });
}

// Execute all statements sequentially
async function executeAllStatements() {
  console.log(`Executing ${statements.length} SQL statements...`);
  
  for (let i = 0; i < statements.length; i++) {
    try {
      await executeStatement(statements[i]);
      console.log(`Statement ${i + 1}/${statements.length} executed successfully.`);
    } catch (error) {
      console.error(`Failed to execute statement ${i + 1}/${statements.length}.`);
    }
  }
  
  console.log('All statements executed.');
}

// Run the script
executeAllStatements().catch(error => {
  console.error('Error executing SQL statements:', error);
  process.exit(1);
});
