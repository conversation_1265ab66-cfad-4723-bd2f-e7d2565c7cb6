# Trustee System Cleanup

This document outlines the steps to clean up and fix issues with the trustee system in Legalock using the Supabase Management API.

## Background

The trustee system has been experiencing persistent issues with:
- Adding trustees
- Sending invitations
- Accepting invitations

These issues are likely caused by:
1. Inconsistent database schema
2. Restrictive Row Level Security (RLS) policies
3. Multiple activation endpoints with different behaviors
4. User ID mapping inconsistencies
5. Token management issues

## Cleanup Steps

### 1. Check the Current Trustee System Status

Before making any changes, check the current status of the trustee system:

```bash
# Make the script executable
chmod +x scripts/check-trustee-system.sh

# Run the script
./scripts/check-trustee-system.sh
```

This will show you the current state of the trustees table, RLS policies, SQL functions, and any problematic trustee records.

### 2. Run the Database Cleanup Script

This script will fix inconsistencies in the database schema, simplify RLS policies, and create reliable SQL functions using the Supabase Management API:

```bash
# Make the script executable
chmod +x scripts/run-trustee-cleanup.sh

# Run the script
./scripts/run-trustee-cleanup.sh
```

### 3. Deploy the SQL Functions

This script will deploy the SQL functions needed for reliable trustee activation and invitation using the Supabase Management API:

```bash
# Make the script executable
chmod +x scripts/deploy-trustee-functions.sh

# Run the script
./scripts/deploy-trustee-functions.sh
```

### 4. Fix Specific Trustee Issues

If you have specific trustee records that are causing problems, you can fix them individually:

```bash
# Make the script executable
chmod +x scripts/fix-specific-trustee.sh

# Run the script with a specific trustee ID
./scripts/fix-specific-trustee.sh <trustee_id>
```

### 5. Verify the Changes

After running the cleanup scripts, check the status again to verify the changes:

```bash
./scripts/check-trustee-system.sh
```

This will confirm that:
1. The `trustees` table has all required columns
2. The RLS policies are simplified
3. The SQL functions are deployed
4. The `trustee_operations_log` table exists
5. There are no problematic trustee records

## Code Changes

The following code changes have been made:

1. **Consolidated Trustee Activation API**:
   - Updated `src/app/api/trustees/activate/route.ts` to handle all activation scenarios

2. **Improved Invitation Process**:
   - Updated `src/app/api/send-trustee-invitation/route.ts` to be more reliable
   - Added token cleanup and better error handling

3. **SQL Functions**:
   - Added `activate_trustee` function for reliable activation
   - Added `direct_update_trustee_invitation` function for reliable invitation updates

## Testing

After implementing these changes, test the trustee system by:

1. Adding a new trustee
2. Sending an invitation
3. Accepting the invitation with a new account
4. Accepting the invitation with an existing account

## Troubleshooting

If issues persist:

1. Check the logs for specific error messages
2. Verify that the SQL functions are properly deployed
3. Check for any RLS policy conflicts
4. Verify that the user mapping is working correctly

You can run the following script to get detailed diagnostic information:

```bash
./scripts/check-trustee-system.sh
```

### Common Issues and Solutions

#### Issue: Trustee invitation not being sent
- Check if the trustee record exists in the database
- Verify that the email address is correct
- Check if there are any errors in the logs when sending the invitation

#### Issue: Trustee activation fails
- Check if the trustee record exists and has the correct status
- Verify that the user ID mapping is correct
- Check if there are any RLS policy conflicts

#### Issue: Multiple activation attempts
- Run the fix script for the specific trustee ID:
  ```bash
  ./scripts/fix-specific-trustee.sh <trustee_id>
  ```

## Additional Notes

- The cleanup process preserves all existing trustee data
- All operations use the Supabase Management API to ensure consistency with the remote database
- The new system is more resilient to errors and edge cases
- The consolidated API simplifies future maintenance
- The scripts can be run multiple times without causing issues
