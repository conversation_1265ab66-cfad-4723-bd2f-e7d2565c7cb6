const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// Read the SQL file
const sqlFilePath = path.join(__dirname, 'fix_remote_db.sql');
const sql = fs.readFileSync(sqlFilePath, 'utf8');

// Start the MCP server
console.log('Starting MCP server...');
const mcpProcess = execSync('npx @supabase/mcp-server-supabase@latest --access-token ******************************************** start', { 
  stdio: 'inherit',
  timeout: 10000 // 10 seconds timeout
});

// Wait for the server to start
console.log('Waiting for MCP server to start...');
setTimeout(() => {
  try {
    // Execute the SQL query
    console.log('Executing SQL query...');
    
    // Create a temporary file with the SQL query
    const tempSqlFile = path.join(__dirname, 'temp_query.sql');
    fs.writeFileSync(tempSqlFile, sql);
    
    // Execute the query using curl to the MCP server
    execSync(`curl -X POST http://localhost:54321/sql -H "Content-Type: application/json" -d '{"query": "$(cat ${tempSqlFile} | tr '\\n' ' ')"}'`, {
      stdio: 'inherit'
    });
    
    // Clean up the temporary file
    fs.unlinkSync(tempSqlFile);
    
    console.log('SQL query executed successfully!');
  } catch (error) {
    console.error('Error executing SQL query:', error.message);
  } finally {
    // Stop the MCP server
    console.log('Stopping MCP server...');
    try {
      execSync('npx @supabase/mcp-server-supabase@latest stop', { 
        stdio: 'inherit'
      });
    } catch (stopError) {
      console.error('Error stopping MCP server:', stopError.message);
    }
  }
}, 5000); // Wait 5 seconds for the server to start
