#!/bin/bash

# Run the trustee database cleanup script using Supabase Management API
echo "Starting trustee database cleanup..."

# Set variables
PROJECT_ID="ccwvtcudztphwwzzgwvg"
SUPABASE_URL="https://ccwvtcudztphwwzzgwvg.supabase.co"
SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM"

# Read the SQL script content
SQL_CONTENT=$(cat scripts/cleanup-trustees-database.sql)

# Split the SQL script into manageable chunks
echo "Executing SQL via Supabase REST API..."

# 1. Fix inconsistent trustee records
echo "Fixing inconsistent trustee records..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "UPDATE trustees SET status = '\''pending'\'' WHERE status IS NULL;"}' \
  --silent | jq

# 2. Add missing columns
echo "Adding missing columns..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "ALTER TABLE trustees ADD COLUMN IF NOT EXISTS inviter_email TEXT, ADD COLUMN IF NOT EXISTS inviter_first_name TEXT, ADD COLUMN IF NOT EXISTS inviter_last_name TEXT, ADD COLUMN IF NOT EXISTS invitation_accepted_at TIMESTAMP WITH TIME ZONE;"}' \
  --silent | jq

# 3. Ensure permissions is always a valid JSONB array
echo "Fixing permissions..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"UPDATE trustees SET permissions = ARRAY[]::text[] WHERE permissions IS NULL OR permissions = ARRAY[]::text[] OR permissions = '{null}'::text[];\"}" \
  --silent | jq

# 4. Clean up orphaned trustee records
echo "Cleaning up orphaned records..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"DELETE FROM trustees WHERE user_id NOT IN (SELECT id FROM auth.users);\"}" \
  --silent | jq

# 5. Clean up expired tokens
echo "Cleaning up expired tokens..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"DELETE FROM trustee_tokens WHERE expires_at < NOW();\"}" \
  --silent | jq

# 6. Create trustee_operations_log table if it doesn't exist
echo "Creating trustee_operations_log table..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"CREATE TABLE IF NOT EXISTS trustee_operations_log (id UUID PRIMARY KEY DEFAULT gen_random_uuid(), operation TEXT NOT NULL, trustee_id UUID NOT NULL, performed_by UUID, details JSONB, created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW());\"}" \
  --silent | jq

# 7. Simplify RLS policies
echo "Simplifying RLS policies..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"DROP POLICY IF EXISTS \\\"Users can view their own trustees\\\" ON public.trustees; DROP POLICY IF EXISTS \\\"Users can insert their own trustees\\\" ON public.trustees; DROP POLICY IF EXISTS \\\"Users can update their own trustees\\\" ON public.trustees; DROP POLICY IF EXISTS \\\"Users can delete their own trustees\\\" ON public.trustees; DROP POLICY IF EXISTS \\\"Trustees can view their invitations\\\" ON public.trustees; DROP POLICY IF EXISTS \\\"Service role can manage all trustees\\\" ON public.trustees;\"}" \
  --silent | jq

# 8. Create new simplified policies
echo "Creating new RLS policies..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"CREATE POLICY \\\"Users can manage their own trustees\\\" ON public.trustees USING (auth.uid() = user_id OR auth.uid() = trustee_user_id);\"}" \
  --silent | jq

curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": \"CREATE POLICY \\\"Service role can manage all trustees\\\" ON public.trustees USING (auth.jwt() ->> 'role' = 'service_role');\"}" \
  --silent | jq

echo "Database cleanup complete!"
