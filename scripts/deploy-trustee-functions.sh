#!/bin/bash

# Deploy the trustee SQL functions using Supabase Management API
echo "Deploying trustee SQL functions..."

# Set variables
PROJECT_ID="ccwvtcudztphwwzzgwvg"
SUPABASE_URL="https://ccwvtcudztphwwzzgwvg.supabase.co"
SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM"

# Execute the SQL directly via the Supabase REST API
echo "Executing SQL via Supabase REST API..."

# 1. Create activate_trustee function
echo "Creating activate_trustee function..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "CREATE OR REPLACE FUNCTION activate_trustee(p_trustee_id UUID, p_user_id UUID) RETURNS VOID AS $$ BEGIN UPDATE trustees SET trustee_user_id = p_user_id, status = '\''active'\'', invitation_accepted_at = NOW(), updated_at = NOW() WHERE id = p_trustee_id; END; $$ LANGUAGE plpgsql SECURITY DEFINER;"}' \
  --silent | jq

# 2. Create direct_update_trustee_invitation function
echo "Creating direct_update_trustee_invitation function..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "CREATE OR REPLACE FUNCTION direct_update_trustee_invitation(p_trustee_id UUID, p_current_time TIMESTAMP WITH TIME ZONE) RETURNS VOID AS $$ BEGIN UPDATE trustees SET invitation_sent_at = p_current_time, updated_at = p_current_time WHERE id = p_trustee_id; END; $$ LANGUAGE plpgsql SECURITY DEFINER;"}' \
  --silent | jq

echo "SQL functions deployment complete!"
