#!/bin/bash

# Fix a specific trustee using Supabase Management API
echo "Fixing specific trustee..."

# Check if trustee ID is provided
if [ -z "$1" ]; then
  echo "Error: Trustee ID is required"
  echo "Usage: ./fix-specific-trustee.sh <trustee_id>"
  exit 1
fi

TRUSTEE_ID=$1

# Set variables
PROJECT_ID="ccwvtcudztphwwzzgwvg"
SUPABASE_URL="https://ccwvtcudztphwwzzgwvg.supabase.co"
SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM"

# Execute the SQL directly via the Supabase REST API
echo "Executing SQL via Supabase REST API..."

# 1. Check the current state of the trustee
echo "Checking current state of trustee $TRUSTEE_ID..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{\"sql\": \"SELECT * FROM trustees WHERE id = '$TRUSTEE_ID';\"}" \
  --silent | jq

# 2. Fix any NULL values
echo "Fixing NULL values..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{\"sql\": \"UPDATE trustees SET status = COALESCE(status, 'pending'), permissions = COALESCE(permissions, '[]'::jsonb), updated_at = NOW() WHERE id = '$TRUSTEE_ID';\"}" \
  --silent | jq

# 3. Clean up any duplicate tokens
echo "Cleaning up duplicate tokens..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{\"sql\": \"DELETE FROM trustee_tokens WHERE trustee_id = '$TRUSTEE_ID' AND id NOT IN (SELECT id FROM trustee_tokens WHERE trustee_id = '$TRUSTEE_ID' ORDER BY expires_at DESC LIMIT 1);\"}" \
  --silent | jq

# 4. Check the fixed state
echo "Checking fixed state..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{\"sql\": \"SELECT * FROM trustees WHERE id = '$TRUSTEE_ID';\"}" \
  --silent | jq

echo "Trustee fix complete!"
