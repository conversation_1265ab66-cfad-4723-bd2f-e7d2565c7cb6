#!/bin/bash

# Check the status of the trustee system using Supabase Management API
echo "Checking trustee system status..."

# Set variables
PROJECT_ID="ccwvtcudztphwwzzgwvg"
SUPABASE_URL="https://ccwvtcudztphwwzzgwvg.supabase.co"
SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM"

# SQL query to check the status
CHECK_SQL=$(cat << 'EOL'
-- Check trustees table structure
SELECT
  column_name,
  data_type,
  is_nullable
FROM
  information_schema.columns
WHERE
  table_schema = 'public'
  AND table_name = 'trustees'
ORDER BY
  ordinal_position;

-- Check RLS policies
SELECT
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM
  pg_policies
WHERE
  schemaname = 'public'
  AND tablename = 'trustees';

-- Check SQL functions
SELECT
  proname,
  prosrc
FROM
  pg_proc
WHERE
  proname IN ('activate_trustee', 'direct_update_trustee_invitation')
  AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- Check trustee_operations_log table
SELECT EXISTS (
  SELECT FROM information_schema.tables
  WHERE table_schema = 'public'
  AND table_name = 'trustee_operations_log'
) AS trustee_operations_log_exists;

-- Count trustees by status
SELECT
  status,
  COUNT(*)
FROM
  trustees
GROUP BY
  status;

-- Check for problematic trustees
SELECT
  id,
  user_id,
  trustee_email,
  trustee_user_id,
  status,
  invitation_sent_at,
  invitation_accepted_at
FROM
  trustees
WHERE
  (status = 'active' AND trustee_user_id IS NULL)
  OR (status = 'pending' AND trustee_user_id IS NOT NULL)
  OR (status IS NULL)
  OR (permissions IS NULL);
EOL
)

# Execute the SQL directly via the Supabase REST API
echo "Executing SQL via Supabase REST API..."

# Check trustees table structure
echo "Checking trustees table structure..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_schema = '\''public'\'' AND table_name = '\''trustees'\'' ORDER BY ordinal_position;"}' \
  --silent | jq '.result'

# Check RLS policies
echo "Checking RLS policies..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "SELECT tablename, policyname, permissive, roles, cmd, qual FROM pg_policies WHERE schemaname = '\''public'\'' AND tablename = '\''trustees'\'';"}' \
  --silent | jq '.result'

# Check SQL functions
echo "Checking SQL functions..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "SELECT proname, prosrc FROM pg_proc WHERE proname IN ('\''activate_trustee'\'', '\''direct_update_trustee_invitation'\'') AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = '\''public'\'');"}' \
  --silent | jq '.result'

# Check trustee_operations_log table
echo "Checking trustee_operations_log table..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = '\''public'\'' AND table_name = '\''trustee_operations_log'\'') AS trustee_operations_log_exists;"}' \
  --silent | jq '.result'

# Count trustees by status
echo "Counting trustees by status..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "SELECT status, COUNT(*) FROM trustees GROUP BY status;"}' \
  --silent | jq '.result'

# Check for problematic trustees
echo "Checking for problematic trustees..."
curl -X POST "$SUPABASE_URL/rest/v1/rpc/exec_sql" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"sql": "SELECT id, user_id, trustee_email, trustee_user_id, status, invitation_sent_at, invitation_accepted_at FROM trustees WHERE (status = '\''active'\'' AND trustee_user_id IS NULL) OR (status = '\''pending'\'' AND trustee_user_id IS NOT NULL) OR (status IS NULL) OR (permissions IS NULL);"}' \
  --silent | jq '.result'

echo "Trustee system check complete!"
