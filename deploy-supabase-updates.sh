#!/bin/bash

# Exit on error
set -e

echo "Deploying Supabase configuration updates..."

# Login to Supabase (this will open a browser window)
echo "Logging in to Supabase..."
supabase login

# Link to the project
echo "Linking to Supabase project..."
supabase link --project-ref ccwvtcudztphwwzzgwvg

# Push the updated configuration
echo "Pushing updated configuration..."
supabase db push

# Deploy the updated edge functions
echo "Deploying updated edge functions..."

# First, deploy the _shared directory if it exists
if [ -d "supabase/functions/_shared" ]; then
  echo "Deploying _shared directory..."
  supabase functions deploy _shared
fi

# Deploy the send-trustee-invitation function
echo "Deploying send-trustee-invitation function..."
supabase functions deploy send-trustee-invitation

# Set the necessary secrets
echo "Setting up secrets..."
supabase secrets set --env-file .env.local

echo "Supabase updates deployed successfully!"
