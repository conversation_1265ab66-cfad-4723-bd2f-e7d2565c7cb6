#!/bin/bash

# Create a temporary migration file
mkdir -p supabase/migrations
cat > supabase/migrations/99999999999999_fix_trustees.sql << 'EOL'
-- Create auth_users_mapping table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.auth_users_mapping (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_user_id UUID NOT NULL,
    auth_user_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(custom_user_id),
    UNIQUE(auth_user_id)
);

-- Create custom_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.custom_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    first_name TEX<PERSON>,
    last_name TEX<PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Check if trustees table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_tables
        WHERE schemaname = 'public' AND tablename = 'trustees'
    ) THEN
        -- Create trustees table if it doesn't exist
        CREATE TABLE public.trustees (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL,
            trustee_email TEXT NOT NULL,
            trustee_user_id UUID,
            first_name TEXT,
            last_name TEXT,
            relationship TEXT,
            status TEXT DEFAULT 'pending',
            permissions JSONB,
            invitation_sent_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );
    ELSE
        -- Check if the foreign key constraint exists and drop it if it does
        IF EXISTS (
            SELECT 1 FROM pg_constraint WHERE conname = 'trustees_user_id_fkey'
        ) THEN
            ALTER TABLE public.trustees DROP CONSTRAINT trustees_user_id_fkey;
        END IF;
    END IF;
END $$;

-- Add foreign key constraint to trustees table
ALTER TABLE public.trustees
ADD CONSTRAINT trustees_user_id_fkey
FOREIGN KEY (user_id)
REFERENCES auth.users(id)
ON DELETE CASCADE;

-- Check if the foreign key constraint exists and drop it if it does
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'trustees_trustee_user_id_fkey'
    ) THEN
        ALTER TABLE public.trustees DROP CONSTRAINT trustees_trustee_user_id_fkey;
    END IF;
END $$;

-- Add foreign key constraint for trustee_user_id
ALTER TABLE public.trustees
ADD CONSTRAINT trustees_trustee_user_id_fkey
FOREIGN KEY (trustee_user_id)
REFERENCES auth.users(id)
ON DELETE SET NULL;

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    email TEXT NOT NULL UNIQUE,
    first_name TEXT,
    last_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create RLS policies for trustees table
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view their own trustees" ON public.trustees;
    DROP POLICY IF EXISTS "Users can insert their own trustees" ON public.trustees;
    DROP POLICY IF EXISTS "Users can update their own trustees" ON public.trustees;
    DROP POLICY IF EXISTS "Users can delete their own trustees" ON public.trustees;
    
    -- Enable RLS on trustees table
    ALTER TABLE public.trustees ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Users can view their own trustees"
    ON public.trustees FOR SELECT
    USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can insert their own trustees"
    ON public.trustees FOR INSERT
    WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "Users can update their own trustees"
    ON public.trustees FOR UPDATE
    USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can delete their own trustees"
    ON public.trustees FOR DELETE
    USING (auth.uid() = user_id);
END $$;
EOL

# Run the migration
echo "Running migration..."
supabase db reset

# Clean up
echo "Cleaning up..."
rm -f supabase/migrations/99999999999999_fix_trustees.sql

echo "Database update complete!"
