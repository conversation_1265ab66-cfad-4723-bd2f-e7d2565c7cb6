#!/bin/bash

# <PERSON>ript to pull current remote Supabase configuration
echo "Pulling current remote Supabase configuration..."

# Create a backup of the current config if it exists
if [ -f "supabase/config.toml" ]; then
  echo "Creating backup of current config..."
  cp supabase/config.toml supabase/config.toml.backup.$(date +%Y%m%d%H%M%S)
fi

# Use MCP to pull the current configuration
echo "Using MCP to pull configuration..."
mkdir -p supabase_temp
npx @supabase/mcp-server-supabase@latest --access-token ******************************************** config > supabase_temp/remote_config.toml

# Check if the config was pulled successfully
if [ -f "supabase_temp/remote_config.toml" ]; then
  echo "Remote configuration pulled successfully."
  
  # Create the supabase directory if it doesn't exist
  mkdir -p supabase
  
  # Copy the remote config to the supabase directory
  cp supabase_temp/remote_config.toml supabase/config.toml
  
  echo "Remote configuration saved to supabase/config.toml"
  
  # Display the config
  echo "Current remote configuration:"
  cat supabase/config.toml
else
  echo "Failed to pull remote configuration."
fi

# Clean up
rm -rf supabase_temp

echo "Done."
