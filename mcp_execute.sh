#!/bin/bash

# Start the MCP server in the background
echo "Starting MCP server..."
npx @supabase/mcp-server-supabase@latest --access-token ******************************************** start &
MCP_PID=$!

# Wait for the server to start
echo "Waiting for MCP server to start..."
sleep 5

# Execute the SQL query
echo "Executing SQL query..."
curl -X POST http://localhost:54321/sql \
  -H "Content-Type: application/json" \
  -d @- << EOF
{
  "query": "$(cat fix_remote_db.sql | tr '\n' ' ')"
}
EOF

# Kill the MCP server
echo "Stopping MCP server..."
kill $MCP_PID

echo "Done!"
