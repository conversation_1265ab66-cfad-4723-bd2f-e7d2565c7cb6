#!/bin/bash

# Find all files that import from auth-utils.ts and update them to use auth-utils-conditional.ts
find src -type f -name "*.ts" -o -name "*.tsx" | xargs grep -l "from '@/lib/auth-utils'" | xargs sed -i '' "s/from '@\/lib\/auth-utils'/from '@\/lib\/auth-utils-conditional'/g"

# Find all files that import from auth-utils-edge.ts and update them to use auth-utils-conditional.ts
find src -type f -name "*.ts" -o -name "*.tsx" | xargs grep -l "from '@/lib/auth-utils-edge'" | xargs sed -i '' "s/from '@\/lib\/auth-utils-edge'/from '@\/lib\/auth-utils-conditional'/g"

echo "Updated all files to use auth-utils-conditional"
